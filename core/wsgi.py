"""
WSGI config for core project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application
from whitenoise import White<PERSON><PERSON>

from core.settings import STATIC_ROOT, STATIC_URL

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

application = get_wsgi_application()
# application = WhiteNoise(application, root=STATIC_ROOT, prefix=STATIC_URL)
