from django.contrib.auth.decorators import user_passes_test
from django.urls import reverse_lazy


def employee_required(function=None, login_url=None):

    def check_employee(user):
        return user.is_authenticated and user.groups.filter(name='employees').exists()

    actual_decorator = user_passes_test(
        check_employee, login_url=login_url or reverse_lazy('login'))
    if function:
        return actual_decorator(function)
    return actual_decorator
