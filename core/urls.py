from django.contrib import admin
from django.urls import path, include, re_path
from . import settings
from django.conf.urls.static import static

admin.site.site_header = "CSR Admin"
admin.site.site_title = "CSR Admin Portal"
admin.site.index_title = "CSR Limited"

urlpatterns = [
    path("admin/", admin.site.urls),
    path("", include("website.urls")),
    path("blog/", include("blog.urls")),
    path("office/", include("backOffice.urls")),
    path("api/accounts/", include("api.urls.accounts")),
    path("api/blog/", include("api.urls.blog")),
    path("api/contact/", include("api.urls.contact")),
    path("api/subscription/", include("api.urls.subscription")),
    path("ckeditor/", include("ckeditor_uploader.urls")),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
