from django.test import TestCase
from rest_framework.test import APIClient
from django.contrib.auth.models import User, Group
from datetime import datetime, timedelta
import jwt
from blog.factory import BlogFactory
from core.factory import UserFactory
from core.settings import SECRET_KEY


class BaseSetup(TestCase):

    def setUp(self):
        self.user = UserFactory()
        self.client = APIClient()

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""

        token_payload = {
            "user_id": user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        self.client = APIClient()
        self.client.force_authenticate(user=user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")
