body {
  position: relative;
}

input,
textarea {
  width: 100%;
  border: 1px solid #3d3d3d;
  font-family: "Futura Book", sans-serif;
}
input::-moz-placeholder, textarea::-moz-placeholder {
  font-size: 16px;
}
input::placeholder,
textarea::placeholder {
  font-size: 16px;
}
input:focus,
textarea:focus {
  border: 1px solid #2A2D7C;
}

input[type=submit] {
  width: 200px;
  font-size: 18px;
  background-color: #2A2D7C;
  color: white;
  border: 1px solid #2A2D7C;
}
input[type=submit]:hover {
  background-color: white;
  color: #2A2D7C;
}

.submit-btn {
  border-radius: 2px;
  background-color: #2A2D7C;
  color: #f6f9fe;
  border: transparent;
  width: 201px;
  padding: 12px 25px;
  display: flex;
  align-items: center;
  gap: 15px;
}
.submit-btn a {
  color: #f6f9fe;
  text-transform: capitalize;
}

.page-header {
  background-color: #2A2D7C;
  color: white;
  padding: 32px 0;
}
.page-header .container {
  display: grid;
  gap: 24px;
}
.page-header .container .menu-search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-header .container .menu-search .menu {
  display: flex;
  gap: 32px;
  align-items: center;
}
.page-header .container .menu-search .menu .menu-item .hover-line {
  height: 2px;
  background-color: white;
  width: 0%;
  margin-top: 6px;
  transition: linear 0.5s;
}
.page-header .container .menu-search .menu .menu-item:hover .hover-line {
  background-color: white;
  width: 50%;
}
.page-header .container .menu-search .menu .active .hover-line {
  width: 50%;
}
.page-header .container .menu-search .search {
  width: 40%;
}

.services-cards {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 24px;
  padding: 50px 0;
}
.services-cards .card {
  width: 280px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: linear 0.3s;
}
.services-cards .card:hover {
  margin-top: -10px;
  position: relative;
}
.services-cards .card p a {
  color: white;
}
.services-cards .software {
  background: url("./img/fotis-fotopoulos-DuHKoV44prg-unsplash.jpg");
  background-color: rgba(0, 0, 0, 0.444);
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
}
.services-cards .network {
  background: url("./img/jordan-harrison-40XgDxBfYXM-unsplash.jpg");
  background-color: rgba(0, 0, 0, 0.35);
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
}
.services-cards .hardware {
  background: url("./img/alexandre-debieve-FO7JIlwjOtU-unsplash.jpg");
  background-color: rgba(0, 0, 0, 0.35);
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
}
.services-cards .printer {
  background: url("./img/mahrous-houses-5AoOejjRUrA-unsplash.jpg");
  background-color: rgba(0, 0, 0, 0.35);
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
}

.progress-line {
  position: relative;
  height: 15px;
}
.progress-line .small {
  height: 2px;
  background-color: #2A2D7C;
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
}
.progress-line .big {
  height: 15px;
  background-color: #2A2D7C;
  border-radius: 6px;
  width: 40%;
  position: absolute;
  top: 0;
  left: 0;
}

.tech-wise-blogs {
  padding: 50px 0;
  display: grid;
  gap: 24px;
  grid-template-columns: 588px 588px;
}
.tech-wise-blogs .blog {
  display: flex;
  gap: 20px;
  align-items: center;
  background-color: whitesmoke;
  border-radius: 2px;
  transition: all linear 0.3s;
}
.tech-wise-blogs .blog img {
  width: 232px;
  height: 179px;
  -o-object-fit: cover;
     object-fit: cover;
}
.tech-wise-blogs .blog .blog-content {
  display: grid;
  gap: 10px;
}
.tech-wise-blogs .blog .blog-content .blog-date {
  color: #686868;
  font-size: 12px;
}
.tech-wise-blogs .blog:hover {
  margin-top: -10px;
  margin-bottom: 10px;
  position: relative;
}
.tech-wise-blogs .blog:hover .blog-content .blog-title {
  text-decoration: underline;
}

.view-all-btn {
  padding-bottom: 50px;
}
.view-all-btn .view-btn {
  background-color: #2A2D7C;
  color: white;
  padding: 17px 25px;
  border-radius: 2px;
}

.bot-bubble {
  width: 460px;
  height: 600px;
  position: fixed;
  bottom: 120px;
  right: 50px;
  display: grid;
  background-color: white;
  gap: 24px;
  border-radius: 20px;
  box-shadow: 6px 4px 30px 1px rgba(0, 0, 0, 0.25);
  transition: linear 0.3s;
}
.bot-bubble .bot-header {
  background-color: #2A2D7C;
  border-radius: 20px 20px 0 0;
  padding: 24px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}
.bot-bubble .bot-header .bot-button {
  position: unset;
}
.bot-bubble .bot-header .bot-header-content {
  display: grid;
  gap: 6px;
}
.bot-bubble .bot-header .bot-header-content .status {
  display: flex;
  gap: 6px;
  align-items: center;
}
.bot-bubble .bot-header .bot-header-content .status .dot {
  height: 12px;
  width: 12px;
  background-color: rgb(40, 208, 40);
  border-radius: 12px;
}
.bot-bubble .bot-header .bot-close-btn {
  background-color: white;
  height: 30px;
  width: 30px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 24px;
  right: 24px;
}
.bot-bubble .bot-header .bot-close-btn i {
  color: #2A2D7C;
}
.bot-bubble .bot-message {
  display: flex;
  align-items: start;
  justify-content: space-between;
  padding: 24px;
  height: 300px;
  overflow-y: scroll;
}
.bot-bubble .bot-message .bot-profile {
  background-color: white;
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  border: 1px solid #2A2D7C;
}
.bot-bubble .bot-message .bot-profile img {
  width: 32px;
}
.bot-bubble .bot-message .message-container {
  width: 350px;
  background-color: whitesmoke;
  padding: 12px;
  border-radius: 6px;
}
.bot-bubble .bot-message .message-container p {
  font-size: 16px;
}
.bot-bubble .message-input {
  padding-right: 24px;
  margin-left: 88px;
  margin-right: 24px;
  margin-bottom: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  border: 1px solid whitesmoke;
  background-color: rgb(225, 225, 225);
}
.bot-bubble .message-input input {
  border: none;
  background-color: transparent;
  outline: none;
}

.bot-header-content {
  background-color: #2A2D7C;
  position: relative;
}

.bot-button {
  position: fixed;
  bottom: 20px;
  right: 50px;
  width: 90px;
  height: 90px;
  border-radius: 90px;
  border: 1px solid #2A2D7C;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
}
.bot-button img {
  width: 45px;
}

.bot-hidden {
  visibility: hidden;
}

@media screen and (max-width: 540px) {
  body {
    display: none;
  }
}/*# sourceMappingURL=styles.css.map */