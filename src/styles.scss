@import '../static/css/variables';

body {
    position: relative;
}

input,
textarea {
    width: 100%;
    border: 1px solid $tex-color;
    font-family: 'Futura Book', sans-serif;

    &::placeholder {
        font-size: 16px;
    }

    &:focus {
        border: 1px solid $primary-color;
    }
}

input[type="submit"] {
    width: 200px;
    font-size: 18px;
    background-color: $primary-color;
    color: white;
    border: 1px solid $primary-color;

    &:hover {
        background-color: white;
        color: $primary-color;
    }
}

.submit-btn {
    border-radius: $border-radius;
    background-color: $primary-color;
    color: $whitesmoke;
    border: transparent;
    width: 201px;
    padding: 12px 25px;
    display: flex;
    align-items: center;
    gap: 15px;

    a {
        color: $whitesmoke;
        text-transform: capitalize;
    }
}

.page-header {
    background-color: $primary-color;
    color: white;
    padding: 32px 0;

    .container {
        display: grid;
        gap: 24px;

        .menu-search {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .menu {
                display: flex;
                gap: 32px;
                align-items: center;

                .menu-item {
                    .hover-line {
                        height: 2px;
                        background-color: white;
                        width: 0%;
                        margin-top: 6px;
                        transition: linear 0.5s;
                    }

                    &:hover {
                        .hover-line {
                            background-color: white;
                            width: 50%;
                        }
                    }

                }

                .active {
                    .hover-line {
                        width: 50%;
                    }
                }
            }

            .search {
                width: 40%;
            }
        }
    }
}

//Services cards
.services-cards {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 24px;
    padding: 50px 0;

    .card {
        width: 280px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        transition: linear 0.3s;

        &:hover {
            margin-top: -10px;
            position: relative;
        }

        p {
            a {
                color: white;
            }
        }
    }

    .software {
        background: url('./img/fotis-fotopoulos-DuHKoV44prg-unsplash.jpg');
        background-color: rgba(0, 0, 0, 0.444);
        background-size: cover;
        background-position: center;
        background-blend-mode: overlay;
    }

    .network {
        background: url('./img/jordan-harrison-40XgDxBfYXM-unsplash.jpg');
        background-color: rgba(0, 0, 0, 0.35);
        background-size: cover;
        background-position: center;
        background-blend-mode: overlay;
    }

    .hardware {
        background: url('./img/alexandre-debieve-FO7JIlwjOtU-unsplash.jpg');
        background-color: rgba(0, 0, 0, 0.35);
        background-size: cover;
        background-position: center;
        background-blend-mode: overlay;
    }

    .printer {
        background: url('./img/mahrous-houses-5AoOejjRUrA-unsplash.jpg');
        background-color: rgba(0, 0, 0, 0.35);
        background-size: cover;
        background-position: center;
        background-blend-mode: overlay;
    }
}

//Progress line

.progress-line {
    position: relative;
    height: 15px;

    .small {
        height: 2px;
        background-color: $primary-color;
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;

    }

    .big {
        height: 15px;
        background-color: $primary-color;
        border-radius: 6px;
        width: 40%;
        position: absolute;
        top: 0;
        left: 0;
    }
}

//blog

.tech-wise-blogs {
    padding: 50px 0;
    display: grid;
    gap: 24px;
    grid-template-columns: 588px 588px;

    .blog {
        display: flex;
        gap: 20px;
        align-items: center;
        background-color: whitesmoke;
        border-radius: 2px;
        transition: all linear 0.3s;

        img {
            width: 232px;
            height: 179px;
            object-fit: cover;
        }

        .blog-content {
            display: grid;
            gap: 10px;

            .blog-date {
                color: #686868;
                font-size: 12px;
            }
        }

        &:hover {
            margin-top: -10px;
            margin-bottom: 10px;
            position: relative;

            .blog-content {
                .blog-title {
                    text-decoration: underline;
                }
            }
        }


    }
}

// view all button

.view-all-btn {
    padding-bottom: 50px;

    .view-btn {
        background-color: $primary-color;
        color: white;
        padding: 17px 25px;
        border-radius: 2px;

    }
}


.bot-bubble {
    width: 460px;
    height: 600px;
    position: fixed;
    bottom: 120px;
    right: 50px;
    display: grid;
    background-color: white;
    gap: 24px;
    border-radius: 20px;
    box-shadow: 6px 4px 30px 1px rgba(0, 0, 0, 0.25);
    transition: linear 0.3s;

    .bot-header {
        background-color: $primary-color;
        border-radius: 20px 20px 0 0;
        padding: 24px;
        position: relative;
        display: flex;
        align-items: center;
        gap: 12px;
        color: white;

        .bot-button {
            position: unset;
        }

        .bot-header-content {
            display: grid;
            gap: 6px;

            .status {
                display: flex;
                gap: 6px;
                align-items: center;

                .dot {
                    height: 12px;
                    width: 12px;
                    background-color: rgb(40, 208, 40);
                    border-radius: 12px;
                }
            }
        }

        .bot-close-btn {
            background-color: white;
            height: 30px;
            width: 30px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 24px;
            right: 24px;

            i {
                color: $primary-color;
            }
        }
    }

    .bot-message {
        display: flex;
        align-items: start;
        justify-content: space-between;
        padding: 24px;
        height: 300px;
        overflow-y: scroll;

        .bot-profile {
            background-color: white;
            height: 50px;
            width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50px;
            border: 1px solid $primary-color;

            img {
                width: 32px;
            }
        }

        .message-container {
            width: 350px;
            background-color: whitesmoke;
            padding: 12px;
            border-radius: 6px;


            p {
                font-size: 16px;
            }
        }
    }

    .message-input {
        padding-right: 24px;
        margin-left: 88px;
        margin-right: 24px;
        margin-bottom: 24px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        border: 1px solid whitesmoke;

        background-color: rgb(225, 225, 225);

        input {
            border: none;
            background-color: transparent;
            outline: none;
        }

    }
}

.bot-header-content {
    background-color: $primary-color;
    position: relative;
}

.bot-button {
    position: fixed;
    bottom: 20px;
    right: 50px;
    width: 90px;
    height: 90px;
    border-radius: 90px;
    border: 1px solid $primary-color;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;

    img {
        width: 45px;
    }
}

//Open the bubble

.bot-hidden {
    visibility: hidden;
}



@media screen and (max-width: 540px) {
    body {
        display: none;
    }
}