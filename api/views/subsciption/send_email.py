from postmarker.core import PostmarkClient
from dotenv import load_dotenv
import os


load_dotenv()

POSTMARK_API_KEY = os.getenv("POSTMARK_API_KEY")
CSR_EMAIL = os.getenv("CSR_EMAIL")

def send_email(email):
    """ Send an email to the specified email address """
    client = PostmarkClient(POSTMARK_API_KEY)
    try:
        client.emails.send(
            From=CSR_EMAIL, 
            To=email,
            Subject="Thanks for subscribing",
            HtmlBody=open("api/templates/subscription_email.html").read(),
            )
        print({"message": "Email sent"})
    except Exception as e:
        print(e)

def unsubscribe_email(email):
    """ Send an email to the specified email address """
    client = PostmarkClient(POSTMARK_API_KEY)
    try:
        client.emails.send(
            From=CSR_EMAIL, 
            To=email,
            Subject="We're sorry to see you go",
            HtmlBody=open("api/templates/unsubscription_email.html").read(),
            )
        print({"message": "<PERSON><PERSON> sent"})
    except Exception as e:
        print(e)