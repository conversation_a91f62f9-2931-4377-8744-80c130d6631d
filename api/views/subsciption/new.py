# new_subscription_api
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view

from website.models import Subscription
from api.views.subsciption.send_email import send_email


@api_view(["POST"])
def new_subscription_api(request):

    try:
        if (
            not "email" in request.data
            or "phone_number" not in request.data
            or "accepted" not in request.data
        ):
            return Response(
                {"error": "Missing required fields: email, phone_number, accepted"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if Subscription.objects.filter(email=request.data["email"]).exists():
            return Response(
                {"error": "A subscription with this email already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not request.data.get("accepted"):
            return Response(
                {"error": "You must accept the terms and conditions"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        new_subscription, created = Subscription.objects.get_or_create(
            email=request.data["email"],
            phone_number=request.data["phone_number"],
            accepted=(request.data["accepted"]),
        )

        if created:
            """ Send an email to the specified email address """
            send_email(new_subscription.email)
            """ Return the created subscription """
            return Response(
                {
                    "message": "Your subscription has been saved",
                    "data": {
                        "email": new_subscription.email,
                        "phone_number": new_subscription.phone_number,
                        "accepted": new_subscription.accepted,
                        "id": new_subscription.id,
                        "date_created": new_subscription.date_created,
                    },
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {
                    "error": "A subscription with this email and phone number already exists"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    except Exception as e:
        print(e)
        return Response(
            {"message": "Error creating subscription"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

