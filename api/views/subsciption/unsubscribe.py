from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view

from website.models import Subscription
from api.views.subsciption.send_email import unsubscribe_email


@api_view(["POST"])
def unsubscribe_api(request):
    """ Delete a subscription """
    try:
        if not "email" in request.data:
            return Response(
                {"error": "Missing required fields: email",
                 "use_case_example": {
                     "email": "<EMAIL>"
                 }},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not Subscription.objects.filter(email=request.data["email"]).exists():
            return Response(
                {"error": f"A subscription with {request.data['email']} does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        subscription = Subscription.objects.get(email=request.data["email"])
        subscription.delete()
        unsubscribe_email(subscription.email) 
        return Response(
            {"message": "Your subscription has been deleted",
             "user": {
                 "email": subscription.email,
                 "phone_number": subscription.phone_number,
             }},
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        print(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )