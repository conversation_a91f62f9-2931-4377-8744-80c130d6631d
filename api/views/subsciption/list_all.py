from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view

from website.models import Subscription


@api_view(["GET"])
def list_all_subscribers(request):
    """ List all subscribers """
    all_subscibers = []
    try:
        subscriptions = Subscription.objects.all()
        for subscriber in subscriptions:
            all_subscibers.append(
                {
                    "email": subscriber.email,
                    "phone_number": subscriber.phone_number,
                    "accepted": subscriber.accepted,
                    "date_created": subscriber.date_created,
                }
            )
        return Response(all_subscibers, status=status.HTTP_200_OK)
    except Exception as e:
        print(e)