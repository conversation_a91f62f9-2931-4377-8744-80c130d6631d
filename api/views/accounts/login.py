from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from rest_framework import authentication
from rest_framework import exceptions
from django.utils import timezone
from django.contrib.auth.models import User
import uuid

from blog.models import APIKey


@api_view(["POST"])
def login_api(request):
    email = request.data.get("email")
    password = request.data.get("password")

    if not email or not password:
        return Response(
            {"error": "Email and password are required"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        user = User.objects.get(email=email)

        authenticated_user = authenticate(username=user.username, password=password)

        if authenticated_user is None:
            return Response(
                {"message": "Invalid email or password"},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        access_token = AccessToken.for_user(user)
        refresh_token = RefreshToken.for_user(user)

        return Response(
            {
                "access": str(access_token),
                "refresh": str(refresh_token),
            },
            status=status.HTTP_200_OK,
        )

    except User.DoesNotExist:
        return Response(
            {"message": "Invalid email or password"},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    except Exception as e:
        print(e)
        return Response(
            {"message": "Error authenticating with backend"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


class APIKeyAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        api_key = request.META.get("HTTP_X_API_KEY")
        if not api_key:
            raise exceptions.AuthenticationFailed("API key not provided")

        try:
            uuid_obj = uuid.UUID(api_key)
        except ValueError:
            raise exceptions.AuthenticationFailed("Invalid API key format")

        try:
            api_key_obj = APIKey.objects.select_related("user").get(
                key=uuid_obj, is_active=True
            )
            if api_key_obj.expires_at and api_key_obj.expires_at < timezone.now():
                raise exceptions.AuthenticationFailed("API key has expired")
            if not api_key_obj.user or not api_key_obj.user.is_active:
                raise exceptions.AuthenticationFailed("User inactive or deleted")
            return (api_key_obj.user, api_key_obj)
        except APIKey.DoesNotExist:
            raise exceptions.AuthenticationFailed("Invalid API key")
        except User.DoesNotExist:
            raise exceptions.AuthenticationFailed("User does not exist")
        except Exception as e:
            print(e)
            raise exceptions.AuthenticationFailed("Error authenticating with backend")
