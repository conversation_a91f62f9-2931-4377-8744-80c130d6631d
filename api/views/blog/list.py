from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.accounts.login import APIKeyAuthentication
from blog.models import Blog
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger


@api_view(["GET"])
def blog_list_api(request):
    user = request.user
    print(f"User: {user}")
    try:
        department = request.GET.get("department")
        page_number = request.GET.get("page", 1)
        per_page = request.GET.get("per_page", 10)

        if department:
            blogs = Blog.objects.filter(department__name=department).prefetch_related(
                "department"
            )
        else:
            blogs = Blog.objects.all().prefetch_related("department")
        blog_list = []
        paginator = Paginator(blogs, per_page)

        try:
            paginated_blogs = paginator.page(page_number)
        except PageNotAnInteger:
            paginated_blogs = paginator.page(1)
        except EmptyPage:
            paginated_blogs = paginator.page(paginator.num_pages)

        for blog in paginated_blogs:
            blog_list.append(
                {
                    "id": blog.id,
                    "title": blog.title,
                    "slug": blog.slug,
                    "featured_image": blog.featured_image,
                    "categories": [
                        {
                            "name": category.name,
                            "slug": category.slug,
                        }
                        for category in blog.categories.all()
                    ],
                    "tags": [
                        {
                            "name": tag.name,
                            "slug": tag.slug,
                        }
                        for tag in blog.tags.all()
                    ],
                    "authors": [
                        {
                            "id": author.id,
                            "first_name": author.first_name,
                            "last_name": author.last_name,
                            "email": author.email,
                        }
                        for author in blog.authors.all()
                    ],
                    "short_description": blog.short_description,
                    "content": blog.content,
                    "date_created": blog.date_created,
                    "last_updated": blog.last_updated,
                }
            )

        return Response(blog_list, status=status.HTTP_200_OK)
    except Exception as e:
        print(e)
        return Response(
            {"error": "Could not get blogs"}, status=status.HTTP_400_BAD_REQUEST
        )
