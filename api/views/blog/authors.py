from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response

from blog.models import Author


@api_view(["GET"])
def get_authors_list_api(request):
    try:
        authors = Author.objects.all()

        authors_list = []
        for author in authors:
            authors_list.append(
                {
                    "id": author.id,
                    "first_name": author.first_name,
                    "last_name": author.last_name,
                    "email": author.email,
                }
            )
        return Response(authors_list, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
