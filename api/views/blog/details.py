from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response

from blog.models import Blog


@api_view(["GET"])
def blog_detail_api(request, blog_slug):
    try:
        blog = Blog.objects.get(slug=blog_slug)
        blog_detail = {
            "id": blog.id,
            "title": blog.title,
            "slug": blog.slug,
            "featured_image": blog.featured_image,
            "categories": [
                {
                    "name": category.name,
                    "slug": category.slug,
                }
                for category in blog.categories.all()
            ],
            "tags": [
                {
                    "name": tag.name,
                    "slug": tag.slug,
                }
                for tag in blog.tags.all()
            ],
            "authors": [
                {
                    "id": author.id,
                    "first_name": author.first_name,
                    "last_name": author.last_name,
                    "email": author.email,
                }
                for author in blog.authors.all()
            ],
            "short_description": blog.short_description,
            "content": blog.content,
            "date_created": blog.date_created,
            "last_updated": blog.last_updated,
        }

        return Response(blog_detail, status=status.HTTP_200_OK)
    except Blog.DoesNotExist:
        return Response({"error": "Blog not found"}, status=status.HTTP_400_BAD_REQUEST)
