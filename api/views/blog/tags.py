from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.response import Response
from api.views.accounts.login import APIKeyAuthentication
from blog.models import Tag
from rest_framework.permissions import IsAuthenticated
from django.utils.text import slugify


@api_view(["GET"])
# @permission_classes([IsAuthenticated])
def get_tags_list_api(request):
    try:
        tags = Tag.objects.all()

        tags_list = []
        for tag in tags:
            tags_list.append(
                {
                    "id": tag.id,
                    "name": tag.name,
                    "slug": tag.slug,
                }
            )
        return Response(tags_list, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def new_tag_api(request):
    try:
        name = request.data.get("name")

        if not name:
            return Response(
                {"error": "Name is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        tag, created = Tag.objects.get_or_create(name=name)
        if created:
            tag = {
                "id": tag.id,
                "name": tag.name,
                "slug": tag.slug,
            }
            return Response(
                {"message": "Tag created successfully", "tag": tag},
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"message": "tag is already created", "tag": tag}, status=status.HTTP
            )

    except Exception as e:
        return Response({"error": str(e)}, status.HTTP_400_BAD_REQUEST)


@api_view(["DELETE"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def delete_tag_api(request, tag_id):
    try:
        tag = Tag.objects.get(id=tag_id)
        tag.delete()
        return Response(
            {"message": "Tag deleted successfully"}, status=status.HTTP_200_OK
        )
    except Tag.DoesNotExist:
        return Response({"error": "Tag not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(["PUT"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def update_tag_api(request, tag_id):
    try:
        tag = Tag.objects.get(id=tag_id)
        name = request.data.get("name")

        if not name:
            return Response(
                {"error": "Name is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        tag.name = name
        tag.slug = slugify(name)
        tag.save()

        tag = {
            "id": tag.id,
            "name": tag.name,
            "slug": tag.slug,
        }
        return Response(
            {"message": "Tag updated successfully", "tag": tag},
            status=status.HTTP_200_OK,
        )
    except Tag.DoesNotExist:
        return Response({"error": "Tag not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
