from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils.text import slugify

from api.views.accounts.login import APIKeyAuthentication
from blog.models import Category


@api_view(["GET"])
def get_categories_list_api(request):
    try:
        categories = Category.objects.all()

        categories_list = []
        for category in categories:
            categories_list.append(
                {
                    "id": category.id,
                    "name": category.name,
                    "slug": category.slug,
                }
            )
        return Response(categories_list, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def new_category_api(request):
    try:
        name = request.data.get("name")

        if not name:
            return Response(
                {"error": "Name is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        category, created = Category.objects.get_or_create(name=name)
        if created:
            new_category = {
                "id": category.id,
                "name": category.name,
                "slug": category.slug,
            }
            return Response(
                {"message": "Category created successfully", "category": new_category},
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {
                "id": category.id,
                "name": category.name,
                "slug": category.slug,
            },
            status=status.HTTP_201_CREATED,
        )
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["DELETE"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def delete_category_api(request, category_id):
    try:
        category = Category.objects.get(id=category_id)
        category.delete()
        return Response(
            {"message": "Category deleted successfully"}, status=status.HTTP_200_OK
        )
    except Category.DoesNotExist:
        return Response(
            {"error": "Category not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(["PUT"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def update_category_api(request, category_id):
    try:
        category = Category.objects.get(id=category_id)
        name = request.data.get("name")

        if not name:
            return Response(
                {"error": "Name is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        category.name = name
        category.slug = slugify(name)
        category.save()

        updated_category = {
            "id": category.id,
            "name": category.name,
            "slug": category.slug,
        }
        return Response(
            {"message": "Category updated successfully", "category": updated_category},
            status=status.HTTP_200_OK,
        )
    except Category.DoesNotExist:
        return Response(
            {"error": "Category not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
