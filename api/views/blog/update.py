from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.accounts.login import APIKeyAuthentication
from blog.models import Author, Blog, Category, Tag


@api_view(["POST"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def update_blog_api(request, blog_slug):
    try:
        blog = Blog.objects.get(slug=blog_slug)
        blog_data = request.data

        # Validate required fields
        required_fields = ["title", "content", "categories", "status"]
        for field in required_fields:
            if field not in blog_data:
                return Response(
                    {"error": f"{field.capitalize()} is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Handle categories
        blog_categories = []
        for category in blog_data.get("categories", []):
            if not category.get("name"):
                return Response(
                    {"error": "Category name is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            category_obj, _ = Category.objects.get_or_create(name=category["name"])
            blog_categories.append(category_obj)

        # Handle multiple authors
        authors = []
        if "authors" in blog_data:
            for author_data in blog_data["authors"]:
                if not author_data.get("first_name") or not author_data.get("email"):
                    return Response(
                        {"error": "Author name and email are required"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                author, _ = Author.objects.get_or_create(
                    email=author_data["email"],
                    defaults={
                        "first_name": author_data["first_name"],
                        "last_name": author_data.get("last_name"),
                    },
                )
                authors.append(author)

        # Handle tags
        blog_tags = []
        for tag in blog_data.get("tags", []):
            if not tag.get("name"):
                return Response(
                    {"error": "Tag name is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            tag_obj, _ = Tag.objects.get_or_create(name=tag["name"])
            blog_tags.append(tag_obj)

        # Update the blog

        # handle change of slug:
        from django.utils.text import slugify

        if blog.slug != slugify(blog_data["title"]):
            blog.slug = slugify(blog_data["title"])
        blog.title = blog_data["title"]
        blog.status = blog_data["status"]
        blog.content = blog_data["content"]
        blog.short_description = blog_data.get("short_description")
        blog.featured_image = blog_data.get("featured_image")

        blog.authors.set(authors)
        blog.categories.set(blog_categories)
        blog.tags.set(blog_tags)

        blog.save()

        # Prepare the response data
        blog_response = {
            "id": blog.id,
            "title": blog.title,
            "slug": blog.slug,
            "featured_image": blog.featured_image,
            "categories": [
                {"name": category.name, "slug": category.slug}
                for category in blog.categories.all()
            ],
            "tags": [{"name": tag.name, "slug": tag.slug} for tag in blog.tags.all()],
            "authors": [
                {
                    "id": author.id,
                    "first_name": author.first_name,
                    "last_name": author.last_name,
                    "email": author.email,
                }
                for author in blog.authors.all()
            ],
            "short_description": blog.short_description,
            "content": blog.content,
            "date_created": blog.date_created,
            "last_updated": blog.last_updated,
        }
        return Response(
            {"message": "Blog updated successfully", "blog": blog_response},
            status=status.HTTP_200_OK,
        )

    except Blog.DoesNotExist:
        return Response({"error": "Blog not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
