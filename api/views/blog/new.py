from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.accounts.login import APIKeyAuthentication
from blog.models import Author, Blog, Category, Tag
from blog.services.blog import BlogService


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([APIKeyAuthentication])
def new_blog_api(request):
    blog_data = request.data

    # Handle categories
    blog_categories = []
    for category in blog_data.get("categories", []):
        if not category.get("name"):
            return Response(
                {"error": "Category name is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        category_obj, created = Category.objects.get_or_create(name=category["name"])
        blog_categories.append(category_obj)

    # Handle multiple authors
    authors = []
    if "authors" in blog_data:
        for author_data in blog_data["authors"]:
            if not author_data.get("first_name") or not author_data.get("email"):
                return Response(
                    {"error": "Author name and email are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            author = None
            try:
                author = Author.objects.get(email=author_data["email"])
            except Author.DoesNotExist:
                author = Author.objects.create(
                    first_name=author_data["first_name"],
                    last_name=author_data.get("last_name"),
                    email=author_data["email"],
                )
            authors.append(author)

    # Handle tags
    blog_tags = []
    for tag in blog_data.get("tags", []):
        if not tag.get("name"):
            return Response(
                {"error": "Tag name is required"}, status=status.HTTP_400_BAD_REQUEST
            )
        tag_obj, created = Tag.objects.get_or_create(name=tag["name"])
        blog_tags.append(tag_obj)

    # Create a new blog
    blog_obj, created = Blog.objects.get_or_create(
        title=blog_data["title"],
        content=blog_data["content"],
        short_description=blog_data.get("short_description"),
        featured_image=blog_data.get("featured_image"),
    )

    if created:
        blog_obj.authors.add(*authors)
        blog_obj.categories.add(*blog_categories)
        blog_obj.tags.add(*blog_tags)

        created_blog = {
            "id": blog_obj.id,
            "title": blog_obj.title,
            "slug": blog_obj.slug,
            "featured_image": blog_obj.featured_image,
            "categories": [
                {
                    "name": category.name,
                    "slug": category.slug,
                }
                for category in blog_obj.categories.all()
            ],
            "tags": [
                {
                    "name": tag.name,
                    "slug": tag.slug,
                }
                for tag in blog_obj.tags.all()
            ],
            "authors": [
                {
                    "id": author.id,
                    "first_name": author.first_name,
                    "last_name": author.last_name,
                    "email": author.email,
                }
                for author in blog_obj.authors.all()
            ],
            "short_description": blog_obj.short_description,
            "content": blog_obj.content,
            "date_created": blog_obj.date_created,
            "last_updated": blog_obj.last_updated,
        }
        return Response(
            {"message": "Blog created successfully", "blog": created_blog},
            status=status.HTTP_201_CREATED,
        )
    else:
        return Response(
            {"error": "A blog with this title already exists"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["POST"])
@authentication_classes([APIKeyAuthentication])
@permission_classes([IsAuthenticated])
def new_blog_api_test(request):
    blog_data = request.data

    # Validate required fields
    required_fields = ["title", "content", "categories"]
    for field in required_fields:
        if field not in blog_data:
            return Response(
                {"error": f"{field.capitalize()} is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    # Delegate to the service layer
    result = BlogService.create_blog(blog_data)

    # Handle the response
    if "error" in result:
        return Response(result, status=status.HTTP_400_BAD_REQUEST)
    return Response(result, status=status.HTTP_201_CREATED)
