import cloudinary
import cloudinary.uploader
from cloudinary import CloudinaryImage
from django.core.files.uploadedfile import InMemoryUploadedFile
from PIL import Image
import io
from datetime import datetime
import uuid
from core.settings import CLOUDINARY_NAME, CLOUDINARY_API_KEY, CLOUDINARY_SECRET_KEY

cloudinary.config(
    cloud_name=CLOUDINARY_NAME,
    api_key=CLOUDINARY_API_KEY,
    api_secret=CLOUDINARY_SECRET_KEY,
    secure=True,
)


def compress_image(image):
    try:
        img = Image.open(image)
        width, height = img.size
        new_width = 800
        coefficient = width / new_width
        new_height = int(height / coefficient)
        img = img.resize((new_width, new_height))

        if img.mode in ("RGBA", "LA"):
            background = Image.new("RGB", img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[3])
            img = background
        image_io = io.BytesIO()
        img.save(image_io, format="JPEG", quality=85)

        image_io.seek(0)

        img.close()
        return image_io.getvalue()
    except Exception as e:
        print(f"Error compressing image: {str(e)}")
        raise


from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status


@api_view(["POST"])
def upload_images(request):
    images = request.FILES.getlist("file")
    folder_name = request.data.get("folder_name")

    results = []
    for image in images:
        result, url, message = upload_image(image, "back-office")
        results.append(url)

    return Response({"urls": results[0]})


def upload_image(image, folder_name):
    try:
        public_id = f"{folder_name}/{datetime.now().year}/{datetime.now().month}/{datetime.now().day}/{image}-{uuid.uuid4()}"
        print("un_resided_image: ", image)
        resized_image = compress_image(image)
        compressed_image = InMemoryUploadedFile(
            io.BytesIO(resized_image),
            None,
            image.name,
            "image/jpeg",
            len(resized_image),
            None,
        )
        print("Resized image: ", compressed_image)
        response = cloudinary.uploader.upload(
            resized_image,
            public_id=public_id,
            unique_filename=True,
            overwrite=False,
            preset_name="v3x0jd9r",
        )
        print("public id from cloudinary is: ", public_id)
        print("response from cloudinary is: ", response)
        return (True, response["secure_url"], "image uploaded")
    except Exception as e:
        print(e)
        return (True, None, str(e))
