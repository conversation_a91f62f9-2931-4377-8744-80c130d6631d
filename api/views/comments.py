from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.core.exceptions import ValidationError

from blog.serializers import CommentSerializer
from blog.services.comment import CommentService
from core.services.logging import LoggingService

comment_service = CommentService()
logging_service = LoggingService()


@api_view(["GET"])
def get_blog_comments(request, blog_id):
    """Get all comments for a specific blog with pagination."""
    try:
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size", 20))
        approved_only = (
            request.query_params.get("approved_only", "true").lower() == "true"
        )

        response = comment_service.get_blog_comments(
            blog_id=blog_id, approved_only=approved_only, page=page, page_size=page_size
        )

        if not response.success:
            return Response({"error": response.message}, status=response.status_code)

        serializer = CommentSerializer(response.data["items"], many=True)
        return Response(
            {
                "data": serializer.data,
                "meta": {
                    "total": response.data["total"],
                    "total": response.data["pages"],
                    "current": response.data["current"],
                    "has_next": response.data["has_next"],
                    "has_prev": response.data["has_prev"],
                },
            },
            status=response.status_code,
        )
    except ValueError as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Invalid pagination parameters"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An unexpected error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
def get_comment_replies(request, blog_id, comment_id):
    """Get all replies for a specific comment with pagination."""
    try:
        page = int(request.query_params.get("page", 1))
        page_size = int(request.query_params.get("page_size", 20))

        response = comment_service.get_comment_replies(
            comment_id=comment_id, page=page, page_size=page_size
        )

        if not response.success:
            return Response({"error": response.message}, status=response.status_code)

        # Filter replies for the correct blog
        items = [reply for reply in response.data["items"] if reply.blog_id == blog_id]
        serializer = CommentSerializer(items, many=True)

        return Response(
            {
                "data": serializer.data.items,
                "meta": {
                    "total": response.data["total"],
                    "pages": response.data["pages"],
                    "current": response.data["current"],
                    "has_next": response.data["has_next"],
                    "has_prev": response.data["has_prev"],
                },
            },
            status=response.status_code,
        )
    except ValueError as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Invalid pagination parameters"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An unexpected error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def approve_comment(request, blog_id, comment_id):
    """Approve a specific comment."""
    try:
        # First get the comment to verify it exists and belongs to the blog
        comment_response = comment_service.get_comment(comment_id)

        if not comment_response.success:
            return Response(
                {"error": comment_response.message}, status=comment_response.status_code
            )

        if comment_response.data.blog_id != blog_id:
            return Response(
                {"error": "Comment does not belong to this blog"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        approve_response = comment_service.approve_comment(comment_id)
        if not approve_response.success:
            return Response(
                {"error": approve_response.message}, status=approve_response.status_code
            )

        return Response(
            {"message": approve_response.message}, status=approve_response.status_code
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An unexpected error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
def create_comment(request, blog_id):
    """Create a new comment for a specific blog."""
    try:
        serializer = CommentSerializer(data=request.data, partial=True)
        if not serializer.is_valid():
            for error in serializer.errors.values():
                logging_service.log_error(error)
            return Response(
                {"error": "Validation error", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        data = {"blog_id": blog_id, **serializer.validated_data}
        response = comment_service.create_comment(data)

        if not response.success:
            return Response({"error": response.message}, status=response.status_code)

        response_serializer = CommentSerializer(response.data)
        return Response(
            {"message": response.message, "data": response_serializer.data},
            status=response.status_code,
        )
    except ValidationError as e:
        logging_service.log_error(e)
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An unexpected error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
