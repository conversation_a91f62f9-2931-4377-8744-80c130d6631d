from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from website.models import ContactMessage
from core.spam_check import is_spam


@api_view(["POST"])
# @permission_classes([IsAuthenticated])
def contact_form_api(request):
    name = request.data.get("name")
    phone_number = request.data.get("phone_number")
    email = request.data.get("email")
    message = request.data.get("message")

    # Validate required fields
    if not name or not phone_number or not email or not message:
        return Response(
            {"error": "All fields are required"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    if is_spam(message):
        return Response(
            {
                "error": "Your message contains spam content. Please try again. if this was a mistake, please call our company number"
            },
            status=status.HTTP_406_NOT_ACCEPTABLE,
        )
    # Validate email format if it contains common email patterns

    # Create a new contact message
    contact_message, created = ContactMessage.objects.get_or_create(
        name=name,
        phone_number=phone_number,
        email=email,
        message=message,
    )

    if created:
        return Response(
            {"message": "Your message has been sent successfully"},
            status=status.HTTP_200_OK,
        )

    else:
        return Response(
            {
                "error": "A message with this same content already exists. Our team will be notified"
            },
            status=status.HTTP_200_OK,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_all_messages_api(request):
    messages = ContactMessage.objects.all()

    messages_data = [
        {
            "id": message.id,
            "name": message.name,
            "phone_number": message.phone_number,
            "email": message.email,
            "message": message.message,
            "date_created": message.date_created,
        }
        for message in messages
    ]

    return Response({"messages": messages_data}, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_message_detail_api(request, message_id):
    try:
        message = ContactMessage.objects.get(id=message_id)
        message_data = {
            "id": message.id,
            "name": message.name,
            "phone_number": message.phone_number,
            "email": message.email,
            "message": message.message,
            "date_created": message.date_created,
        }
        return Response(message_data, status=status.HTTP_200_OK)
    except ContactMessage.DoesNotExist:
        return Response(
            {"error": "Message not found"}, status=status.HTTP_404_NOT_FOUND
        )
