from django.urls import path

from api.views.blog.authors import get_authors_list_api
from api.views.blog.categories import (
    delete_category_api,
    get_categories_list_api,
    new_category_api,
    update_category_api,
)
from api.views.blog.details import blog_detail_api
from api.views.blog.list import blog_list_api
from api.views.blog.new import new_blog_api, new_blog_api_test
from api.views.blog.tags import (
    delete_tag_api,
    get_tags_list_api,
    new_tag_api,
    update_tag_api,
)
from api.views.blog.update import update_blog_api
from api.views.comments import *
from api.views.media.image_upload import upload_images

urlpatterns = [
    path("upload-images/", upload_images, name="upload_images"),
    path("", blog_list_api, name="blog_list_api"),
    path("details/<blog_slug>/", blog_detail_api, name="blog_detail_api"),
    path("<blog_slug>/update/", update_blog_api, name="update_blog_api"),
    # categories
    path("categories/", get_categories_list_api, name="get_categories_list_api"),
    path("categories/new/", new_category_api, name="new_category_api"),
    path(
        "categories/<int:category_id>/delete/",
        delete_category_api,
        name="delete_category_api",
    ),
    path(
        "categories/<int:category_id>/update/",
        update_category_api,
        name="update_category_api",
    ),
    # tags
    path("tags/", get_tags_list_api, name="get_tags_list_api"),
    path("tags/new/", new_tag_api, name="get_tags_list_api"),
    path("tags/<int:tag_id>/delete/", delete_tag_api, name="delete_tag_api"),
    path("tags/<int:tag_id>/update/", update_tag_api, name="update_tag_api"),
    # authors
    path("authors/", get_authors_list_api, name="get_authors_list_api"),
    # new blog
    path("new/", new_blog_api_test, name="new_blog_api"),
    # comments
    path(
        "<int:blog_id>/comments/",
        get_blog_comments,
        name="get_blog_comments",
    ),
    path(
        "/<int:blog_id>/comments/<int:comment_id>/replies/",
        get_comment_replies,
        name="get_comment_replies",
    ),
    path(
        "/<int:blog_id>/comments/<int:comment_id>/approve/",
        approve_comment,
        name="approve_comment",
    ),
    path(
        "<int:blog_id>/comments/create/",
        create_comment,
        name="create_comment",
    ),
]
