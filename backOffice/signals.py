from django.contrib.auth.models import Group
from django.db.models.signals import post_migrate
from django.dispatch import receiver


@receiver(post_migrate)
def create_groups(sender, **kwargs):
    """
    Creates the required groups if they don't exist.
    """
    group_names = [
        'employee',
        'hod',
        'cfo',
        'coo',
        'adminstration',
        'powerUser',
        'ceo',
    ]

    for group_name in group_names:
        Group.objects.get_or_create(name=group_name)
