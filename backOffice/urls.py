from django.urls import path
from .views import loginView, logoutView, dashboard, blogs, blog, addBlog, editBlog, deleteBlog


urlpatterns = [
    path('', dashboard, name='office'),
    path('login', loginView, name='login'),
    path('logout/', logoutView, name='logout'),
    # path('dashboard/', dashboard, name='dashboard'),
    path('blogs/', blogs, name='dash-blogs'),
    # path('blogs/single-blog/<slug:slug>', blog, name='blog'),
    path('add-blog/', addBlog, name='add-blog'),
    path('edit-blog/<pk>', editBlog, name='edit-blog'),
    path('delete-blog/<pk>', deleteBlog, name='delete-blog'),
]
