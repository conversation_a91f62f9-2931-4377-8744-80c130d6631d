
import datetime
import os
from django.conf import settings
from django.core.files.storage import default_storage
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from core.permissions.permissions import employee_required
from django.urls import reverse_lazy

from django.core.files.storage import FileSystemStorage
from django.utils.text import slugify

from blog.models import Blog
from .forms import CustomLoginForm, AddBlogFrom
from django.http import JsonResponse


def loginView(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            return redirect('office')
        else:
            error_msg = 'Invalid username or password'
            return render(request, 'auth/login.html', {'error_msg': error_msg})
    else:
        return render(request, 'auth/login.html')


def logoutView(request):
    logout(request)
    return redirect('login')


@ employee_required
def dashboard(request):
    return render(request, 'backOffice/index.html')


def blogs(request):
    feature = "Blog"
    page_title = "All blogs"
    blogs = Blog.objects.all()
    context = {'blogs': blogs, 'feature': feature, 'page_title': page_title}
    return render(request, 'backOffice/blog/blogs-list.html', context)


def blog(request, slug):
    blog = Blog.objects.get(slug=slug)
    print(blog)
    context = {'blog': blog}
    return render(request, 'backOffice/blog/blog-single.html', context)


# def addBlog(request):
#     feature = "Blog"
#     page_title = "Add blog"
#     if request.method == 'POST':
#         form = AddBlogFrom(request.POST, request.FILES)
#         if form.is_valid():
#             print('Form is valid')
#             blog_instance = AddBlogFrom()
#             blog_instance.title = request.POST.get('title')
#             blog_instance.slug = slugify(request.POST.get('title'))
#             blog_instance.category = form.cleaned_data['category']
#             blog_instance.author = form.cleaned_data['author']
#             blog_instance.featured_image = request.FILES['featured_image']
#             blog_instance.blog_summary = form.cleaned_data['blog_summary']
#             blog_instance.blog_content = form.cleaned_data['blog_content']
#             blog_instance.save()
#             return redirect('dash-blogs')
#         else:
#             print('form is not valid')
#     form = AddBlogFrom()
#     context = {'form': form, 'page_title': page_title, 'feature': feature}
#     return render(request, 'backOffice/blog/add-blog.html', context)

def addBlog(request):
    feature = "Blog"
    page_title = "Add blog"
    if request.method == 'POST':
        form = AddBlogFrom(request.POST, request.FILES)
        if form.is_valid():
            print('Form is valid')
            blog_instance = Blog()
            blog_instance.title = form.cleaned_data.get('title')
            blog_instance.slug = slugify(form.cleaned_data.get('title'))
            blog_instance.category = form.cleaned_data.get('category')
            blog_instance.author = form.cleaned_data.get('author')
            blog_instance.featured_image = request.FILES['featured_image']
            blog_instance.blog_summary = form.cleaned_data.get('blog_summary')
            blog_instance.save()
            return redirect('dash-blogs')
        else:
            print('form is not valid')
    else:
        form = AddBlogFrom()

    context = {'form': form, 'page_title': page_title, 'feature': feature}
    return render(request, 'backOffice/blog/add-blog.html', context)


def editBlog(request, pk):
    feature = "Blog"
    url = 'dash-blogs'
    page_title = "Edit blog"
    blog = Blog.objects.get(id=pk)
    form = AddBlogFrom(instance=blog)
    if request.method == 'POST':
        form = Blog(request.POST, request.FILES, instance=blog)
        if form.is_valid():
            print('Form is valid')
            blog.title = request.POST.get('title')
            blog.slug = slugify(request.POST.get('title'))
            blog.category = form.cleaned_data['category']
            blog.author = form.cleaned_data['author']
            blog.featured_image = request.FILES['featured_image']
            blog.blog_summary = form.cleaned_data['blog_summary']
            blog.blog_content = form.cleaned_data['blog_content']
            blog.save()
            return redirect('dash-blogs')
        else:
            print('form is not valid')

    context = {'blog': blog, 'form': form,
               'feature': feature, 'page_title': page_title, 'url': url}
    return render(request, 'backOffice/blog/add-blog.html', context)


def deleteBlog(request, pk):
    blog = Blog.objects.get(id=pk)
    blog.delete()
    # context = {'blog': blog}
    # if request.method == 'POST':
    #     blog.delete()
    return redirect('dash-blogs')

    # return render(request, 'backOffice/blog/delete.html', context)
