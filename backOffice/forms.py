from django.contrib.auth.forms import AuthenticationForm
from django import forms
from django.forms import ModelForm, TextInput, Textarea, Select
from ckeditor_uploader.widgets import CKEditorUploadingWidget
from blog.models import Blog


class CustomLoginForm(AuthenticationForm):
    username = forms.CharField(
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "Username",
                "name": "username",
            }
        )
    )
    password = forms.CharField(
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": "Password",
                "name": "password",
            }
        )
    )


class AAddBlogForm(forms.ModelForm):
    blog_content = forms.CharField(widget=CKEditorUploadingWidget())

    class Meta:
        model = Blog
        fields = [
            "title",
            "short_description",
            "content",
            "featured_image",
            "slug",
        ]

        widgets = {
            "title": TextInput(attrs={"placeholder": "Add blog title here"}),
            "blog_summary": Textarea(
                attrs={"placeholder": "Add blog summary here", "rows": 5}
            ),
        }


class AddBlogFrom(forms.ModelForm):
    blog_content = forms.CharField(widget=CKEditorUploadingWidget())

    class Meta:
        model = Blog
        fields = [
            "title",
            "blog_content",
            "featured_image",
            "short_description",
        ]
        widgets = {
            "title": TextInput(
                attrs={
                    "placeholder": "Add title here",
                    "name": "title",
                    "class": "block w-full px-4 py-3 mb-2 text-sm placeholder-gray-500 bg-white border rounded",
                }
            ),
            "short_description": Textarea(
                attrs={
                    "name": "short_description",
                    "rows": 5,
                    "placeholder": "Add a small summary for the blog here",
                    "class": "block w-full px-4 py-3 mb-2 text-sm placeholder-gray-500 bg-white border rounded",
                }
            ),
            # 'blog_content': TextInput(attrs={'name': 'blog_content', 'placeholder': 'Add blog content here'}),
            "category": forms.RadioSelect(attrs={"name": "category"}),
            "author": forms.Select(attrs={"name": "author"}),
            "featured_image": forms.ClearableFileInput(
                attrs={"name": "featured_image"}
            ),
        }
