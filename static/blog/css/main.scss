@import '../css/variables';


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    background-color: $color-white;
    font-size: 20px;
    font-family: Arial;
}

a {
    text-decoration: none;
    cursor: pointer;
}

ul {
    list-style-type: none;
}

.container {
    max-width: 1200px;
    padding: 0;
    margin: auto;
}

.row {
    display: flex;
    align-items: center;
}

button {
    border: transparent;
    cursor: pointer;
    background-color: transparent;
}

// some mobile general layouts 
@media screen and (max-width: 540px) {
    .row {
        display: flex;
        flex-direction: column;
    }
}