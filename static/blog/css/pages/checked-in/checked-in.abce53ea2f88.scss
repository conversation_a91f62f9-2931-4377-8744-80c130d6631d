// variables
@import '../../variables';

.checked-in {
    display: grid;
    place-items: center;
    align-content: center;
    height: 80vh;

    .container {
        display: flex;
        flex-direction: column;
        flex-grow: 0;
        justify-content: center;
        align-items: flex-start;
        gap: $default-gap;
        border: 1px solid $color-black;
        border-radius: calc($border-radius * 2);
        padding: 100px;

        .row {
            gap: 12px;

            .heading {
                font-size: 24px;
            }
        }

        a {
            background-color: $color-black;
            color: $color-white;
            padding: 12px 85px;
            border-radius: $border-radius;
            transition: linear 0.2s;
            font-size: 16px;

            &:hover {
                background: transparent;
                color: $color-black;
                border: 1px solid $color-black;
            }
        }
    }
}