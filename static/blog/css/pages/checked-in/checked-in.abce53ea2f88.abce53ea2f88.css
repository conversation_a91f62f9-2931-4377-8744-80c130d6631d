.checked-in {
  display: grid;
  place-items: center;
  align-content: center;
  height: 80vh;
}
.checked-in .container {
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  border: 1px solid #000;
  border-radius: 24px;
  padding: 100px;
}
.checked-in .container .row {
  gap: 12px;
}
.checked-in .container .row .heading {
  font-size: 24px;
}
.checked-in .container a {
  background-color: #000;
  color: #FFFFFF;
  padding: 12px 85px;
  border-radius: 12px;
  transition: linear 0.2s;
  font-size: 16px;
}
.checked-in .container a:hover {
  background: transparent;
  color: #000;
  border: 1px solid #000;
}/*# sourceMappingURL=checked-in.abce53ea2f88.abce53ea2f88.css.map */