.new-password {
  display: grid;
  place-items: center;
  align-content: center;
  height: 80vh;
}
.new-password .container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.new-password .container .row {
  gap: 20px;
}
.new-password .container .row .logo {
  width: 100px;
  height: 100px;
}
.new-password .container .row .heading {
  font-size: 26px;
}
.new-password .container form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.new-password .container form label input {
  width: 350px;
  padding: 12px 20px;
  border-radius: 12px;
  border: 1px solid #000;
}
.new-password .container form .new-password-links {
  margin-top: 12px;
}
.new-password .container form .new-password-links .password-request-btn {
  background-color: #000;
  color: #FFFFFF;
  padding: 17px 35px;
  border-radius: 12px;
  transition: linear 0.2s;
  font-size: 16px;
}
.new-password .container form .new-password-links .password-request-btn:hover {
  background: transparent;
  color: #000;
  border: 1px solid #000;
}
.new-password .container form .new-password-links .login-btn {
  color: #846C6C;
  font-size: 12px;
  text-decoration: underline;
}/*# sourceMappingURL=new-password.css.map */