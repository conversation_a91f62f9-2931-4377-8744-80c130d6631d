// variables
@import '../../variables';

.new-password {
    display: grid;
    place-items: center;
    align-content: center;
    height: 80vh;
    .container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
  
      .row {
        gap: calc($default-gap * 2);
  
        .logo {
          width: 100px;
          height: 100px;
        }
  
        .heading {
          font-size: 26px;
        }
      }
  
      form {
        display: flex;
        flex-direction: column;
        gap: $default-gap;
  
        label {
          input {
            width: 350px;
            padding: 12px 20px;
            border-radius: $border-radius;
            border: 1px solid #000;
          }
        }

        .new-password-links {
          margin-top: 12px;
          .password-request-btn {
              background-color: $color-black;
              color: $color-white;
              padding: 17px 35px;
              border-radius: $border-radius;
              transition: linear 0.2s;
              font-size: 16px;

              &:hover {
                  background: transparent;
                  color: $color-black;
                  border: 1px solid $color-black;
              }
          }

          .login-btn {
              color: $color-brown;
              font-size: 12px;
              text-decoration: underline;
          }
      }
      }
    }
}
  
  