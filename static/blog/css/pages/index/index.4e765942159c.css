.login {
  display: grid;
  place-items: center;
  align-content: center;
  height: 80vh;
}
.login .container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 12px;
}
.login .container .row {
  gap: 20px;
}
.login .container .row .logo {
  width: 50px;
}
.login .container .row .heading {
  font-size: 26px;
}
.login .container form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.login .container form label input {
  width: 350px;
  padding: 12px 20px;
  border-radius: 12px;
  border: 1px solid #000;
}
.login .container form .login-links {
  margin-top: 12px;
}
.login .container form .login-links .login-btn {
  background-color: #000;
  color: #FFFFFF;
  padding: 12px 85px;
  border-radius: 12px;
  transition: linear 0.2s;
  font-size: 16px;
}
.login .container form .login-links .login-btn:hover {
  background: transparent;
  color: #000;
  border: 1px solid #000;
}
.login .container form .login-links .new-password-link {
  color: #846C6C;
  font-size: 12px;
  text-decoration: underline;
}/*# sourceMappingURL=index.css.map */