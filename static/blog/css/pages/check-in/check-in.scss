// variables
@import '../../variables';

.check-in {
    display: grid;
    place-items: center;
    align-content: center;
    height: 80vh;

    .container {
        display: flex;
        flex-direction: column;
        flex-grow: 0;
        justify-content: center;
        align-items: flex-start;
        gap: $default-gap;
        border: 1px solid $color-black;
        border-radius: calc($border-radius * 2);
        padding: 100px;

        .heading {
            width: 471px;
            font-size: 24px;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: $default-gap;
      
            label {
              input {
                width: 350px;
                padding: 12px 20px;
                border-radius: $border-radius;
                border: 1px solid #000;
              }
            }
    
            .check-in-links {
              margin-top: 12px;
              .check-in-btn {
                  background-color: $color-black;
                  color: $color-white;
                  padding: 12px 85px;
                  border-radius: $border-radius;
                  transition: linear 0.2s;
                  font-size: 16px;
    
                  &:hover {
                      background: transparent;
                      color: $color-black;
                      border: 1px solid $color-black;
                  }
              }
    
              .dashboard-btn {
                  color: $color-brown;
                  font-size: 12px;
                  text-decoration: underline;
              }
          }
          }
    }
}