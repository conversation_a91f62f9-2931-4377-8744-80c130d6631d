@import '../../variables';

@media screen and (max-width: 540px) {
    .check-in {
    .container {
        width: 300px;
        padding: 40px 20px;

        .heading {
            width: 250px;
            font-size: 15px;
        }

        form {
            label {
                input {
                    width: 100%;
                }
            }

            .check-in-links {
                display: flex;
                flex-direction: column;
                gap: $default-gap;
            }
        }
    }
}
}