.check-in {
  display: grid;
  place-items: center;
  align-content: center;
  height: 80vh;
}
.check-in .container {
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  border: 1px solid #000;
  border-radius: 24px;
  padding: 100px;
}
.check-in .container .heading {
  width: 471px;
  font-size: 24px;
}
.check-in .container form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.check-in .container form label input {
  width: 350px;
  padding: 12px 20px;
  border-radius: 12px;
  border: 1px solid #000;
}
.check-in .container form .check-in-links {
  margin-top: 12px;
}
.check-in .container form .check-in-links .check-in-btn {
  background-color: #000;
  color: #FFFFFF;
  padding: 12px 85px;
  border-radius: 12px;
  transition: linear 0.2s;
  font-size: 16px;
}
.check-in .container form .check-in-links .check-in-btn:hover {
  background: transparent;
  color: #000;
  border: 1px solid #000;
}
.check-in .container form .check-in-links .dashboard-btn {
  color: #846C6C;
  font-size: 12px;
  text-decoration: underline;
}/*# sourceMappingURL=check-in.04894427d8e3.css.map */