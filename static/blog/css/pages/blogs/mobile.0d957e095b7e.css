@media screen and (max-width: 1120px) {
  .blogs .row .sidebar {
    display: none;
  }
  .blogs .row .fixed-sidebar {
    background-color: #000;
    color: #FFFFFF;
    position: fixed;
    width: 100%;
    left: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 24px;
  }
  .blogs .row .fixed-sidebar .side-bar-content {
    width: 95%;
  }
  .blogs .row .fixed-sidebar .side-bar-content .sidebar-profile {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .blogs .row .fixed-sidebar .side-bar-content .sidebar-profile .close-sidebar {
    display: block;
  }
  .blogs .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .blogs .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .blogs .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header h2 {
    font-size: 16px;
  }
  .blogs .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .blogs .row .fixed-sidebar .close-sidebar {
    display: block;
    text-align: left;
  }
  .blogs .row .fixed-sidebar h3 {
    font-size: 20px;
  }
  .blogs .row .fixed-sidebar .col-1 {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 12px;
    background-color: #3F3F3F;
    padding: 20px;
  }
  .blogs .row .fixed-sidebar .col-1 p {
    font-size: 16px;
  }
  .blogs .row .fixed-sidebar .profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .blogs .row .fixed-sidebar .profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .blogs .row .fixed-sidebar .profile h2 {
    font-size: 16px;
  }
  .blogs .row .fixed-sidebar .profile a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .blogs .row .contents {
    width: 100%;
  }
  .blogs .row .contents nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .blogs .row .contents nav .nav-btn {
    display: block;
  }
  .blogs .row .contents .add-blog-btn {
    margin: 10px 20px;
  }
  .blogs .row .contents .blogs-container {
    padding: 0 20px;
  }
}
@media screen and (max-width: 945px) {
  .blogs .row .contents .add-blog-btn {
    margin: 10px 20px;
  }
  .blogs .row .contents .blogs-container .blog-titles {
    grid-template-columns: 18% 88%;
  }
  .blogs .row .contents .blogs-container .blog-titles .blog-heading {
    display: none;
  }
  .blogs .row .contents .blogs-container .blog-titles .blog-img img {
    max-width: 60px;
  }
  .blogs .row .contents .blogs-container .blog-titles .blog-title-content {
    display: flex;
    flex-direction: column-reverse;
    gap: 6px;
  }
  .blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-img img {
    max-width: 80px;
  }
  .blogs .row .contents .blogs-container .blog-titles .blog-title-content .date-status {
    display: flex;
    justify-content: space-between;
    gap: 12px;
  }
  .blogs .row .contents .blogs-container .blog-titles .blog-title-content .status-title,
  .blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-comments-title,
  .blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-status-title {
    display: none;
  }
  .blogs .row .contents .blogs-container .blog-titles .action-btns {
    display: none;
  }
  .blogs .row .contents .blogs-container .blog-titles .expand-btn {
    display: block;
  }
  .blogs .row .contents .blogs-container .blogs-list {
    pointer-events: none;
  }
}/*# sourceMappingURL=mobile.css.map */