@import "../../variables";

@media screen and (max-width: 1120px) {
    .blogs {
        .row {
            .sidebar {
                display: none;
            }

            .fixed-sidebar {
                background-color: $color-black;
                color: $color-white;
                position: fixed;
                width: 100%;
                left: 0;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;

                .side-bar-content {
                    width: 95%;

                    .sidebar-profile {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .close-sidebar {
                            display: block;
                        }

                        .profile-header {
                            display: flex;
                            align-items: center;
                            gap: $default-gap;
                            padding-top: 20px;

                            img {
                                width: 40px;
                                height: 40px;
                                border-radius: 50%;
                            }

                            h2 {
                                font-size: 16px;
                            }

                            a {
                                font-size: 12px;
                                color: $color-white;
                            }
                        }
                    }
                }

                .close-sidebar {
                    display: block;
                    text-align: left;
                }

                h3 {
                    font-size: 20px;
                }

                .col-1 {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    border-radius: $border-radius;
                    background-color: $color-light-black;
                    padding: 20px;

                    p {
                        font-size: 16px;
                    }
                }

                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }

                    h2 {
                        font-size: 16px;
                    }

                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }

            .contents {
                width: 100%;

                nav {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .nav-btn {
                        display: block;
                    }
                }

                .add-blog-btn {
                    margin: 10px 20px;
                }

                .blogs-container {
                    padding: 0 20px;
                }
            }
        }
    }
}

@media screen and (max-width: 945px) {
    .blogs {
        .row {
            .contents {
                .add-blog-btn {
                    margin: 10px 20px;
                }

                .blogs-container {
                    // padding: 0 20px;
                    .blog-titles {
                        grid-template-columns: 18% 88%;
                        .blog-heading {
                            display: none;
                        }
                        .blog-img {
                            img {
                                max-width: 60px;
                            }
                        }
                        .blog-title-content {
                            display: flex;
                            flex-direction: column-reverse;
                            gap: 6px;
                            .blog-img {
                                img {
                                    max-width: 80px;
                                }
                            }
                            .date-status {
                                display: flex;
                                justify-content: space-between;
                                gap: 12px;
                            }
                            .status-title,
                            .blog-comments-title,
                            .blog-status-title {
                                display: none;
                            }
                        }

                        .action-btns {
                            display: none;
                        }

                        .expand-btn {
                            display: block;
                        }
                    }
                    .blogs-list {
                        pointer-events: none;
                    }
                }
            }
        }
    }
}
