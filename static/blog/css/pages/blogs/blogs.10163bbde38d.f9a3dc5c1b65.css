.blogs .row {
  align-items: flex-start;
  gap: 12px;
  position: relative;
}
.blogs .row .sidebar {
  height: 100vh;
  width: 20%;
  position: relative;
  z-index: 10;
}
.blogs .row .sidebar .side-bar-content {
  background-color: #000;
  color: #FFFFFF;
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 24px;
}
.blogs .row .sidebar .side-bar-content .close-sidebar {
  display: none;
}
.blogs .row .sidebar .side-bar-content .sidebar-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: scroll;
}
.blogs .row .sidebar .side-bar-content .sidebar-cards h3 {
  font-size: 20px;
}
.blogs .row .sidebar .side-bar-content .sidebar-cards .col-1 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  background-color: #3F3F3F;
  padding: 20px;
}
.blogs .row .sidebar .side-bar-content .sidebar-cards .col-1 p {
  font-size: 16px;
}
.blogs .row .sidebar .side-bar-content .profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
}
.blogs .row .sidebar .side-bar-content .profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.blogs .row .sidebar .side-bar-content .profile h2 {
  font-size: 16px;
}
.blogs .row .sidebar .side-bar-content .profile a {
  font-size: 12px;
  color: #FFFFFF;
}
.blogs .row .fixed-sidebar {
  background-color: #000;
  color: #FFFFFF;
  width: 100%;
  left: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 24px;
}
.blogs .row .fixed-sidebar .close-sidebar {
  display: block;
  text-align: left;
}
.blogs .row .fixed-sidebar h3 {
  font-size: 20px;
}
.blogs .row .fixed-sidebar .col-1 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  background-color: #3F3F3F;
  padding: 20px;
}
.blogs .row .fixed-sidebar .col-1 p {
  font-size: 16px;
}
.blogs .row .fixed-sidebar .profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
}
.blogs .row .fixed-sidebar .profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.blogs .row .fixed-sidebar .profile h2 {
  font-size: 16px;
}
.blogs .row .fixed-sidebar .profile a {
  font-size: 12px;
  color: #FFFFFF;
}
.blogs .row .contents {
  position: relative;
  right: 0;
}
.blogs .row .contents nav {
  background-color: #000;
  padding: 20px;
}
.blogs .row .contents nav .nav-btn {
  display: none;
}
.blogs .row .contents nav .links {
  display: flex;
  gap: 20px;
  justify-content: right;
}
.blogs .row .contents nav .links li a {
  color: #FFFFFF;
  font-size: 16px;
}
.blogs .row .contents .add-blog-btn {
  background-color: #1890FF;
  padding: 15px 25px;
  margin-top: 20px;
  overflow-x: scroll;
}
.blogs .row .contents .blogs-container {
  width: 78vw;
}
.blogs .row .contents .blogs-container .blog-titles {
  width: 100%;
  display: grid;
  grid-template-columns: 8% 92%;
  gap: 8px;
  padding: 8px;
}
.blogs .row .contents .blogs-container .blog-titles .blog-img img {
  max-width: 80px;
}
.blogs .row .contents .blogs-container .blog-titles .blog-title-content {
  display: grid;
  grid-template-columns: 40% 28% 16% 14%;
  gap: 12px;
}
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-title,
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .action-btn,
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-category-title,
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-date-title,
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-status-title,
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .blog-comments-title {
  font-size: 14px;
}
.blogs .row .contents .blogs-container .blog-titles .blog-title-content .date-status {
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 24px;
}
.blogs .row .contents .blogs-container .blog-titles .action-btns {
  font-size: 14px;
}
.blogs .row .contents .blogs-container .blog-titles .action-btns .edit-btn {
  color: #0C9C00;
}
.blogs .row .contents .blogs-container .blog-titles .action-btns .delete-btn {
  color: #F41212;
}
.blogs .row .contents .blogs-container .blog-titles .action-btns .publish-btn {
  color: #0851A7;
}
.blogs .row .contents .blogs-container .blog-titles .expand-btn {
  font-size: 14px;
  color: #0C9C00;
  display: none;
}
.blogs .row .contents .blogs-container .blogs-list {
  width: 100vw;
  margin-top: 24px;
}
.blogs .row .contents .blogs-container .blogs-list .blog img {
  width: 100%;
  height: 40px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 2px;
}
.blogs .row .contents .blogs-container .blogs-list .blog:hover .action-btns {
  display: inline-block;
}
.blogs .row .contents .blogs-container .scrolling-blog-list {
  height: 80vh;
  overflow: hidden;
  overflow-y: scroll;
}
.blogs .row .contents .footer {
  background-color: #000;
  color: #FFFFFF;
  position: fixed;
  padding: 21px 25px;
  padding-left: 18%;
  bottom: 0;
  right: 0;
  left: 0;
}
.blogs .row .contents .footer h2 {
  font-size: 16px;
}/*# sourceMappingURL=blogs.10163bbde38d.css.map */