@import '../../variables';


.blogs {
    .row {
        align-items: flex-start;
        gap: 12px;
        position: relative;

        .sidebar {
            height: 100vh;
            width: 20%;
            position: relative;
            z-index: 10;

            .side-bar-content {
                background-color: $color-black;
                color: $color-white;
                position: absolute;
                top: 0;
                bottom: 0;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;

                .close-sidebar {
                    display: none;
                }

                .sidebar-cards {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    overflow-y: scroll;

                    h3 {
                        font-size: 20px;
                    }

                    .col-1 {
                        display: flex;
                        flex-direction: column;
                        gap: $default-gap;
                        border-radius: $border-radius;
                        background-color: $color-light-black;
                        padding: 20px;

                        p {
                            font-size: 16px;
                        }
                    }
                }

                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }

                    h2 {
                        font-size: 16px;
                    }

                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }
        }

        .fixed-sidebar {
            background-color: $color-black;
            color: $color-white;
            width: 100%;
            left: 0;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: $default-gap;
            padding: 20px 24px;

            .close-sidebar {
                display: block;
                text-align: left;
            }

            h3 {
                font-size: 20px;
            }

            .col-1 {
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                border-radius: $border-radius;
                background-color: $color-light-black;
                padding: 20px;

                p {
                    font-size: 16px;
                }
            }

            .profile {
                display: flex;
                align-items: center;
                gap: $default-gap;
                padding-top: 20px;

                img {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                }

                h2 {
                    font-size: 16px;
                }

                a {
                    font-size: 12px;
                    color: $color-white;
                }
            }
        }

        .contents {
            position: relative;
            right: 0;

            // nav
            nav {
                background-color: $color-black;
                padding: 20px;

                .nav-btn {
                    display: none;
                }

                .links {
                    display: flex;
                    gap: calc($default-gap * 2);
                    justify-content: right;

                    li {
                        a {
                            color: $color-white;
                            font-size: 16px;
                        }
                    }
                }
            }

            .add-blog-btn {
                background-color: $color-blue;
                padding: 15px 25px;
                margin-top: 20px;
                overflow-x: scroll;

            }

            .blogs-container {
                width: 78vw;

                .blog-titles {
                    width: 100%;
                    display: grid;
                    grid-template-columns: 8% 92%;
                    //grid-template-columns: 5% 71% 12% 12%;
                    //grid-template-columns: 5% 43% 16% 12% 12% 12%;
                    gap: 8px;
                    padding: 8px;

                    .blog-img {
                        img {
                            max-width: 80px;
                        }
                    }

                    .blog-title-content {
                        display: grid;
                        grid-template-columns: 40% 28% 16% 14%;
                        gap: 12px;

                        .blog-title,
                        .action-btn,
                        .blog-category-title,
                        .blog-date-title,
                        .blog-status-title,
                        .blog-comments-title {
                            font-size: 14px;
                        }

                        .date-status {
                            display: grid;
                            grid-template-columns: 50% 50%;
                            gap: 24px;
                        }
                    }

                    .action-btns {
                        font-size: 14px;

                        //display: none;
                        .edit-btn {
                            color: $color-green;
                        }

                        .delete-btn {
                            color: $color-red;
                        }

                        .publish-btn {
                            color: $color-dark-blue;
                        }
                    }

                    .expand-btn {
                        font-size: 14px;
                        color: $color-green;
                        display: none;
                    }
                }

                .blogs-list {
                    width: 100vw;
                    margin-top: 24px;

                    .blog {
                        img {
                            width: 100%;
                            height: 40px;
                            object-fit: cover;
                            border-radius: 2px;
                        }

                        &:hover {
                            .action-btns {
                                display: inline-block;
                            }
                        }
                    }
                }

                .scrolling-blog-list {
                    height: 80vh;
                    overflow: hidden;
                    overflow-y: scroll;
                }
            }

            // footer
            .footer {
                background-color: $color-black;
                color: $color-white;
                position: fixed;
                padding: 21px 25px;
                padding-left: 18%;
                bottom: 0;
                right: 0;
                left: 0;

                h2 {
                    font-size: 16px;
                }
            }
        }
    }
}