@media screen and (max-width: 1120px) {
  .add-blog .row .sidebar {
    display: none;
  }
  .add-blog .row .fixed-sidebar {
    background-color: #000;
    color: #FFFFFF;
    position: fixed;
    width: 100%;
    left: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 24px;
  }
  .add-blog .row .fixed-sidebar .side-bar-content {
    width: 95%;
  }
  .add-blog .row .fixed-sidebar .side-bar-content .sidebar-profile {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .add-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .close-sidebar {
    display: block;
  }
  .add-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .add-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .add-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header h2 {
    font-size: 16px;
  }
  .add-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .add-blog .row .fixed-sidebar .close-sidebar {
    display: block;
    text-align: left;
  }
  .add-blog .row .fixed-sidebar h3 {
    font-size: 20px;
  }
  .add-blog .row .fixed-sidebar .col-1 {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 12px;
    background-color: #3F3F3F;
    padding: 20px;
  }
  .add-blog .row .fixed-sidebar .col-1 p {
    font-size: 16px;
  }
  .add-blog .row .fixed-sidebar .profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .add-blog .row .fixed-sidebar .profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .add-blog .row .fixed-sidebar .profile h2 {
    font-size: 16px;
  }
  .add-blog .row .fixed-sidebar .profile a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .add-blog .row .contents {
    width: 100%;
  }
  .add-blog .row .contents nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .add-blog .row .contents nav .nav-btn {
    display: block;
  }
  .add-blog .row .contents .form {
    padding: 0 20px;
  }
  .add-blog .row .contents .blog-col {
    padding: 0 20px;
  }
}
@media screen and (max-width: 768px) {
  .add-blog .row .contents nav .links {
    display: none;
  }
  .add-blog .row .contents .form {
    padding: 0 20px;
    width: 100%;
  }
  .add-blog .row .contents .form .form-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .add-blog .row .contents .form .form-header button {
    background-color: #1890FF;
    padding: 12px;
    font-size: 16px;
  }
  .add-blog .row .contents .form .form-header .minus-btn {
    display: none;
  }
  .add-blog .row .contents .blog-col {
    display: none;
  }
}
@media screen and (max-width: 540px) {
  .add-blog .row .contents .blog-col {
    display: none;
    width: 100%;
    top: 50px;
    bottom: 0;
    background-color: #EEEEEE;
  }
}/*# sourceMappingURL=mobile.c92692db827b.c92692db827b.css.map */