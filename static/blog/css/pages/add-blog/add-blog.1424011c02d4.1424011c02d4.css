.add-blog .row {
  align-items: flex-start;
  gap: 20px;
  position: relative;
}
.add-blog .row .sidebar {
  height: 100vh;
  width: 20%;
  z-index: 10;
}
.add-blog .row .sidebar .side-bar-content {
  background-color: #000;
  color: #FFFFFF;
  position: fixed;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 24px;
}
.add-blog .row .sidebar .side-bar-content .sidebar-profile {
  display: none;
}
.add-blog .row .sidebar .side-bar-content .close-sidebar {
  display: none;
}
.add-blog .row .sidebar .side-bar-content .sidebar-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: scroll;
}
.add-blog .row .sidebar .side-bar-content .sidebar-cards h3 {
  font-size: 20px;
}
.add-blog .row .sidebar .side-bar-content .sidebar-cards .col-1 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  background-color: #3F3F3F;
  padding: 20px;
}
.add-blog .row .sidebar .side-bar-content .sidebar-cards .col-1 p {
  font-size: 16px;
}
.add-blog .row .sidebar .side-bar-content .profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
}
.add-blog .row .sidebar .side-bar-content .profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.add-blog .row .sidebar .side-bar-content .profile h2 {
  font-size: 16px;
}
.add-blog .row .sidebar .side-bar-content .profile a {
  font-size: 12px;
  color: #FFFFFF;
}
.add-blog .row .col {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.add-blog .row .contents {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.add-blog .row .contents nav {
  background-color: #000;
  padding: 20px;
}
.add-blog .row .contents nav .nav-btn {
  display: none;
}
.add-blog .row .contents nav .links {
  display: flex;
  gap: 20px;
  justify-content: right;
}
.add-blog .row .contents nav .links li a {
  color: #FFFFFF;
  font-size: 16px;
}
.add-blog .row .contents .form {
  width: 70%;
}
.add-blog .row .contents .form .form-header {
  display: none;
}
.add-blog .row .contents .form form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.add-blog .row .contents .form form input {
  padding: 12px;
  background-color: #F3F3F3;
  border: none;
}
.add-blog .row .contents .form form textarea {
  padding: 12px;
  background-color: #F3F3F3;
  border: none;
  resize: none;
}
.add-blog .row .contents .form form ::-moz-placeholder {
  font-family: arial;
}
.add-blog .row .contents .form form ::placeholder {
  font-family: arial;
}
.add-blog .row .contents .form form .submit-btn {
  width: 200px;
  background-color: #1890FF;
  cursor: pointer;
  transition: linear 0.3s;
}
.add-blog .row .contents .form form .submit-btn:hover {
  background: none;
  border: 1px solid #1890FF;
}
.add-blog .row .contents .blog-col .buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.add-blog .row .contents .blog-col .buttons .draft-btn {
  background-color: #F3F3F3;
  padding: 15px 25px;
}
.add-blog .row .contents .blog-col .buttons .delete-btn {
  background-color: #F41212;
  padding: 15px 30px;
  color: #FFFFFF;
}
.add-blog .row .contents .blog-col .buttons .changes-btn {
  background-color: #1890FF;
  padding: 15px 25px;
}
.add-blog .row .contents .blog-col .categories {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #EEEEEE;
  padding: 24px;
  margin-top: 10px;
}
.add-blog .row .contents .blog-col .categories h2 {
  font-size: 16px;
  text-decoration: underline;
}
.add-blog .row .contents .blog-col .categories ul li {
  display: flex;
  gap: 10px;
  font-size: 16px;
  padding-top: 10px;
}
.add-blog .row .contents .blog-col .featured-img {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #EEEEEE;
  padding: 24px;
  margin-top: 10px;
}
.add-blog .row .contents .blog-col .featured-img h2 {
  font-size: 16px;
  text-decoration: underline;
}
.add-blog .row .contents .blog-col .featured-img img {
  width: 200px;
}
.add-blog .row .contents .footer {
  background-color: #000;
  color: #FFFFFF;
  padding: 21px 25px;
  padding-left: 18%;
  bottom: 0;
  right: 0;
  left: 0;
}
.add-blog .row .contents .footer h2 {
  font-size: 16px;
}/*# sourceMappingURL=add-blog.1424011c02d4.1424011c02d4.css.map */