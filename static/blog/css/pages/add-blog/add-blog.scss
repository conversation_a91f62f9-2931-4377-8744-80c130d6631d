@import '../../variables';

.add-blog {

    .row {
        align-items: flex-start;
        gap: calc($default-gap * 2);
        position: relative;

        .sidebar {
            height: 100vh;
            width: 20%;
            z-index: 10;

            .side-bar-content {
                background-color: $color-black;
                color: $color-white;
                position: fixed;
                top: 0;
                bottom: 0;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;

                .sidebar-profile {
                    display: none;
                }

                .close-sidebar {
                    display: none;
                }

                .sidebar-cards {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    overflow-y: scroll;

                    h3 {
                        font-size: 20px;
                    }

                    .col-1 {
                        display: flex;
                        flex-direction: column;
                        gap: $default-gap;
                        border-radius: $border-radius;
                        background-color: $color-light-black;
                        padding: 20px;

                        p {
                            font-size: 16px;
                        }
                    }
                }

                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }

                    h2 {
                        font-size: 16px;
                    }

                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }
        }

        .col {
            display: flex;
            flex-direction: column;
            gap: $default-gap;
        }

        .contents {
            width: 80%;
            display: flex;
            flex-direction: column;
            gap: $default-gap;


            nav {
                background-color: $color-black;
                padding: 20px;

                .nav-btn {
                    display: none;
                }

                .links {
                    display: flex;
                    gap: calc($default-gap * 2);
                    justify-content: right;

                    li {
                        a {
                            color: $color-white;
                            font-size: 16px;
                        }
                    }
                }
            }

            .form {
                width: 70%;

                .form-header {
                    display: none;
                }

                form {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;


                    input {
                        padding: 12px;
                        background-color: $color-gray;
                        border: none;
                    }

                    textarea {
                        padding: 12px;
                        background-color: $color-gray;
                        border: none;
                        resize: none;
                    }

                    ::placeholder {
                        font-family: arial;
                    }

                    .submit-btn {
                        width: 200px;
                        background-color: $color-blue;
                        cursor: pointer;
                        transition: linear 0.3s;

                        &:hover {
                            background: none;
                            border: 1px solid $color-blue;
                        }
                    }
                }
            }

            .blog-col {
                .buttons {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;

                    .draft-btn {
                        background-color: $color-gray;
                        padding: 15px 25px;
                    }

                    .delete-btn {
                        background-color: $color-red;
                        padding: 15px 30px;
                        color: $color-white;
                    }

                    .changes-btn {
                        background-color: $color-blue;
                        padding: 15px 25px;
                    }
                }

                .categories {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    background-color: $color-gray-secondary;
                    padding: 24px;
                    margin-top: 10px;

                    h2 {
                        font-size: 16px;
                        text-decoration: underline;
                    }

                    ul {
                        li {
                            display: flex;
                            gap: $default-gap;
                            font-size: 16px;
                            padding-top: 10px;
                        }
                    }
                }

                .featured-img {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    background-color: $color-gray-secondary;
                    padding: 24px;
                    margin-top: 10px;

                    h2 {
                        font-size: 16px;
                        text-decoration: underline;
                    }

                    img {
                        width: 200px;
                    }
                }
            }

            .footer {
                background-color: $color-black;
                color: $color-white;
                //position: fixed;
                padding: 21px 25px;
                padding-left: 18%;
                bottom: 0;
                right: 0;
                left: 0;

                h2 {
                    font-size: 16px;
                }
            }
        }
    }
}