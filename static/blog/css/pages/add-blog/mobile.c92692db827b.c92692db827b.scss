@import '../../variables';

@media screen and (max-width: 1120px) {
    .add-blog {
        .row {
            .sidebar {
                display: none;
            }

            .fixed-sidebar {
                background-color: $color-black;
                color: $color-white;
                position: fixed;
                width: 100%;
                left: 0;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;

                .side-bar-content {
                    width: 95%;

                    .sidebar-profile {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .close-sidebar {
                            display: block;
                        }

                        .profile-header {
                            display: flex;
                            align-items: center;
                            gap: $default-gap;
                            padding-top: 20px;

                            img {
                                width: 40px;
                                height: 40px;
                                border-radius: 50%;
                            }

                            h2 {
                                font-size: 16px;
                            }

                            a {
                                font-size: 12px;
                                color: $color-white;
                            }
                        }
                    }
                }

                .close-sidebar {
                    display: block;
                    text-align: left;
                }

                h3 {
                    font-size: 20px;
                }

                .col-1 {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    border-radius: $border-radius;
                    background-color: $color-light-black;
                    padding: 20px;

                    p {
                        font-size: 16px;
                    }
                }

                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }

                    h2 {
                        font-size: 16px;
                    }

                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }

            .contents {
                width: 100%;

                nav {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .nav-btn {
                        display: block;
                    }
                }

                .form {
                    padding: 0 20px;
                }
                .blog-col {
                    padding: 0 20px;
                }
            }
        }
    }
}

@media screen and (max-width: 768px) {
    .add-blog {
        .row {

            .contents {

                nav {
                    .links {
                        display: none;
                    }
                }

                .form {
                    padding: 0 20px;
                    width: 100%;

                    .form-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        button {
                            background-color: $color-blue;
                            padding: 12px;
                            font-size: 16px;
                        }

                        .minus-btn {
                            display: none;
                        }
                    }
                }
                .blog-col {
                    display: none;
                }
            }
        }
    }
}

@media screen and (max-width: 540px) {
    .add-blog {
        .row {
            .contents {
                .blog-col {
                    display: none;
                    width: 100%;
                    top: 50px;
                    bottom: 0;
                    background-color: $color-gray-secondary;
                }
            }
        }
    }
}