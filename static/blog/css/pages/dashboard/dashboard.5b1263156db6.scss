// variables
@import '../../variables';

.dashboard {
    .row {
        align-items: flex-start;
        gap: calc($default-gap * 2);
        position: relative;

        .sidebar {
            height: 100vh;
            width: 20%;
            position: relative;
            z-index: 10;

            a {
                color: white
            }

            .side-bar-content {
                background-color: $color-black;
                color: $color-white;
                position: absolute;
                top: 0;
                bottom: 0;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;

                .close-sidebar {
                    display: none;
                }

                .sidebar-cards {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    overflow-y: scroll;

                    h3 {
                        font-size: 20px;
                    }

                    .col-1 {
                        display: flex;
                        flex-direction: column;
                        gap: $default-gap;
                        border-radius: $border-radius;
                        background-color: $color-light-black;
                        padding: 20px;

                        p {
                            font-size: 16px;
                        }
                    }
                }

                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }

                    h2 {
                        font-size: 16px;
                    }

                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }
        }


        .fixed-sidebar {
            background-color: $color-black;
            color: $color-white;
            width: 100%;
            left: 0;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: $default-gap;
            padding: 20px 24px;

            .close-sidebar {
                display: block;
                text-align: left;
            }

            h3 {
                font-size: 20px;
            }

            .col-1 {
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                border-radius: $border-radius;
                background-color: $color-light-black;
                padding: 20px;

                p {
                    font-size: 16px;
                }
            }

            .profile {
                display: flex;
                align-items: center;
                gap: $default-gap;
                padding-top: 20px;

                img {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                }

                h2 {
                    font-size: 16px;
                }

                a {
                    font-size: 12px;
                    color: $color-white;
                }
            }
        }

        .contents {
            width: 80%;
            display: flex;
            flex-direction: column;
            gap: calc($default-gap * 2);

            nav {
                background-color: $color-black;
                padding: 20px;

                .nav-btn {
                    display: none;
                }

                .links {
                    display: flex;
                    gap: calc($default-gap * 2);
                    justify-content: right;

                    li {
                        a {
                            color: $color-white;
                            font-size: 16px;
                        }
                    }
                }
            }

            // dashboards
            .row {
                .col {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap * 2;

                    .row-1 {
                        display: flex;
                        align-items: center;
                        gap: $default-gap * 2;

                        .col-1 {
                            background-color: $color-black;
                            color: $color-white;
                            padding: 32px 36px;
                            width: 100%;
                            height: 35vh;

                            h2 {
                                font-size: 16px;
                            }
                        }
                    }
                }

                .col-2 {
                    background-color: $color-black;
                    color: $color-white;
                    padding: 32px 36px;
                    width: 33%;
                    height: 80vh;

                    h2 {
                        font-size: 16px;
                    }
                }
            }

            .footer {
                background-color: $color-black;
                color: $color-white;
                position: fixed;
                padding: 21px 25px;
                padding-left: 18%;
                bottom: 0;
                right: 0;
                left: 0;

                h2 {
                    font-size: 16px;
                }
            }
        }
    }
}