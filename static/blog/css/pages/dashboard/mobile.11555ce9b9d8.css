@media screen and (max-width: 918px) {
  .dashboard .row .sidebar {
    display: none;
  }
  .dashboard .row .fixed-sidebar {
    background-color: #000;
    display: block;
    color: #FFFFFF;
    width: 100%;
    height: 100vh;
    position: fixed;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 24px;
  }
  .dashboard .row .fixed-sidebar h3 {
    font-size: 20px;
  }
  .dashboard .row .fixed-sidebar .col-1 {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 12px;
    background-color: #3F3F3F;
    padding: 20px;
  }
  .dashboard .row .fixed-sidebar .col-1 p {
    font-size: 16px;
  }
  .dashboard .row .fixed-sidebar .profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .dashboard .row .fixed-sidebar .profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .dashboard .row .fixed-sidebar .profile h2 {
    font-size: 16px;
  }
  .dashboard .row .fixed-sidebar .profile a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .dashboard .row .contents {
    width: 100%;
  }
  .dashboard .row .contents nav .nav-btn {
    display: block;
  }
  .dashboard .row .contents nav .links {
    display: none;
  }
  .dashboard .row .contents .row {
    flex-direction: column;
  }
  .dashboard .row .contents .row .col .row-1 {
    flex-direction: column;
  }
  .dashboard .row .contents .row .col .row-1 .col-1 {
    width: 90%;
  }
  .dashboard .row .contents .row .col-2 {
    width: 90%;
    margin-left: 20px;
    margin-bottom: 20px;
  }
  .dashboard .row .contents footer {
    width: 100%;
  }
}/*# sourceMappingURL=mobile.css.map */