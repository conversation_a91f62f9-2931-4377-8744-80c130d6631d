.dashboard .row {
  align-items: flex-start;
  gap: 20px;
  position: relative;
}
.dashboard .row .sidebar {
  height: 100vh;
  width: 20%;
  position: relative;
  z-index: 10;
}
.dashboard .row .sidebar a {
  color: white;
}
.dashboard .row .sidebar .side-bar-content {
  background-color: #000;
  color: #FFFFFF;
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 24px;
}
.dashboard .row .sidebar .side-bar-content .close-sidebar {
  display: none;
}
.dashboard .row .sidebar .side-bar-content .sidebar-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: scroll;
}
.dashboard .row .sidebar .side-bar-content .sidebar-cards h3 {
  font-size: 20px;
}
.dashboard .row .sidebar .side-bar-content .sidebar-cards .col-1 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  background-color: #3F3F3F;
  padding: 20px;
}
.dashboard .row .sidebar .side-bar-content .sidebar-cards .col-1 p {
  font-size: 16px;
}
.dashboard .row .sidebar .side-bar-content .profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
}
.dashboard .row .sidebar .side-bar-content .profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.dashboard .row .sidebar .side-bar-content .profile h2 {
  font-size: 16px;
}
.dashboard .row .sidebar .side-bar-content .profile a {
  font-size: 12px;
  color: #FFFFFF;
}
.dashboard .row .fixed-sidebar {
  background-color: #000;
  color: #FFFFFF;
  width: 100%;
  left: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 24px;
}
.dashboard .row .fixed-sidebar .close-sidebar {
  display: block;
  text-align: left;
}
.dashboard .row .fixed-sidebar h3 {
  font-size: 20px;
}
.dashboard .row .fixed-sidebar .col-1 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  background-color: #3F3F3F;
  padding: 20px;
}
.dashboard .row .fixed-sidebar .col-1 p {
  font-size: 16px;
}
.dashboard .row .fixed-sidebar .profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
}
.dashboard .row .fixed-sidebar .profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.dashboard .row .fixed-sidebar .profile h2 {
  font-size: 16px;
}
.dashboard .row .fixed-sidebar .profile a {
  font-size: 12px;
  color: #FFFFFF;
}
.dashboard .row .contents {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard .row .contents nav {
  background-color: #000;
  padding: 20px;
}
.dashboard .row .contents nav .nav-btn {
  display: none;
}
.dashboard .row .contents nav .links {
  display: flex;
  gap: 20px;
  justify-content: right;
}
.dashboard .row .contents nav .links li a {
  color: #FFFFFF;
  font-size: 16px;
}
.dashboard .row .contents .row .col {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard .row .contents .row .col .row-1 {
  display: flex;
  align-items: center;
  gap: 20px;
}
.dashboard .row .contents .row .col .row-1 .col-1 {
  background-color: #000;
  color: #FFFFFF;
  padding: 32px 36px;
  width: 100%;
  height: 35vh;
}
.dashboard .row .contents .row .col .row-1 .col-1 h2 {
  font-size: 16px;
}
.dashboard .row .contents .row .col-2 {
  background-color: #000;
  color: #FFFFFF;
  padding: 32px 36px;
  width: 33%;
  height: 80vh;
}
.dashboard .row .contents .row .col-2 h2 {
  font-size: 16px;
}
.dashboard .row .contents .footer {
  background-color: #000;
  color: #FFFFFF;
  position: fixed;
  padding: 21px 25px;
  padding-left: 18%;
  bottom: 0;
  right: 0;
  left: 0;
}
.dashboard .row .contents .footer h2 {
  font-size: 16px;
}/*# sourceMappingURL=dashboard.5b1263156db6.5b1263156db6.css.map */