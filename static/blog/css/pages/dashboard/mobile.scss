@import '../../variables';

@media screen and (max-width: 918px) {
    .dashboard {
        .row {
            .sidebar {
                display: none;
            }

            .fixed-sidebar {
                background-color: $color-black;
                display: block;
                color: $color-white;
                width: 100%;
                height: 100vh;
                position: fixed;
                right: 0;
                left: 0;
                top: 0;
                bottom: 0;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;
    
                h3 {
                    font-size: 20px;
                }
    
                .col-1 {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    border-radius: $border-radius;
                    background-color: $color-light-black;
                    padding: 20px;
    
                    p {
                        font-size: 16px;
                    }
                }
    
                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;
    
                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }
    
                    h2 {
                        font-size: 16px;
                    }
    
                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }

            .contents {
                width: 100%;

                nav {
                    .nav-btn {
                        display: block;
                    }
                    .links {
                        display: none;
                    }
                }

                // dashboards 
                .row {
                    flex-direction: column;

                    .col {
                        .row-1 {
                            flex-direction: column;

                            .col-1 {
                                width: 90%;
                            }
                        }
                    }

                    .col-2 {
                        width: 90%;
                        margin-left: 20px;
                        margin-bottom: 20px;
                    }
                }

                footer {
                    width: 100%;
                }
            }
        }
    }
}
