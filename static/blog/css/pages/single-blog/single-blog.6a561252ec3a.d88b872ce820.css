.single-blog .row {
  align-items: flex-start;
  gap: 20px;
  position: relative;
}
.single-blog .row .sidebar {
  height: 100vh;
  width: 20%;
  z-index: 10;
}
.single-blog .row .sidebar .side-bar-content {
  background-color: #000;
  color: #FFFFFF;
  position: fixed;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 24px;
}
.single-blog .row .sidebar .side-bar-content .sidebar-profile {
  display: none;
}
.single-blog .row .sidebar .side-bar-content .close-sidebar {
  display: none;
}
.single-blog .row .sidebar .side-bar-content .sidebar-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: scroll;
}
.single-blog .row .sidebar .side-bar-content .sidebar-cards h3 {
  font-size: 20px;
}
.single-blog .row .sidebar .side-bar-content .sidebar-cards .col-1 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 12px;
  background-color: #3F3F3F;
  padding: 20px;
}
.single-blog .row .sidebar .side-bar-content .sidebar-cards .col-1 p {
  font-size: 16px;
}
.single-blog .row .sidebar .side-bar-content .profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
}
.single-blog .row .sidebar .side-bar-content .profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.single-blog .row .sidebar .side-bar-content .profile h2 {
  font-size: 16px;
}
.single-blog .row .sidebar .side-bar-content .profile a {
  font-size: 12px;
  color: #FFFFFF;
}
.single-blog .row .col {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.single-blog .row .contents {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.single-blog .row .contents nav {
  background-color: #000;
  padding: 20px;
}
.single-blog .row .contents nav .nav-btn {
  display: none;
}
.single-blog .row .contents nav .links {
  display: flex;
  gap: 20px;
  justify-content: right;
}
.single-blog .row .contents nav .links li a {
  color: #FFFFFF;
  font-size: 16px;
}
.single-blog .row .contents .blog {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 70%;
}
.single-blog .row .contents .blog .blog-header {
  display: none;
}
.single-blog .row .contents .blog h1 {
  font-size: 16px;
}
.single-blog .row .contents .blog p {
  font-size: 14px;
}
.single-blog .row .contents .blog .blog-paragraph {
  background-color: #EEEEEE;
  padding: 12px 25px;
}
.single-blog .row .contents .blog .blog-content {
  background-color: #EEEEEE;
  padding: 12px 25px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.single-blog .row .contents .blog .blog-content img {
  width: 60%;
}
.single-blog .row .contents .blog-col .buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.single-blog .row .contents .blog-col .buttons .draft-btn {
  background-color: #F3F3F3;
  padding: 15px 25px;
}
.single-blog .row .contents .blog-col .buttons .delete-btn {
  background-color: #F41212;
  padding: 15px 30px;
  color: #FFFFFF;
}
.single-blog .row .contents .blog-col .buttons .changes-btn {
  background-color: #1890FF;
  padding: 15px 25px;
}
.single-blog .row .contents .blog-col .categories {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #EEEEEE;
  padding: 24px;
  margin-top: 10px;
}
.single-blog .row .contents .blog-col .categories h2 {
  font-size: 16px;
  text-decoration: underline;
}
.single-blog .row .contents .blog-col .categories ul li {
  display: flex;
  gap: 10px;
  font-size: 16px;
  padding-top: 10px;
}
.single-blog .row .contents .blog-col .featured-img {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #EEEEEE;
  padding: 24px;
  margin-top: 10px;
}
.single-blog .row .contents .blog-col .featured-img h2 {
  font-size: 16px;
  text-decoration: underline;
}
.single-blog .row .contents .blog-col .featured-img img {
  width: 200px;
}
.single-blog .row .contents .footer {
  background-color: #000;
  color: #FFFFFF;
  padding: 21px 25px;
  padding-left: 18%;
  bottom: 0;
  right: 0;
  left: 0;
}
.single-blog .row .contents .footer h2 {
  font-size: 16px;
}/*# sourceMappingURL=single-blog.6a561252ec3a.css.map */