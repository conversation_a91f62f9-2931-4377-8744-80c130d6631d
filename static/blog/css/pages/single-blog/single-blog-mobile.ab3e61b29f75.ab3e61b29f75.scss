@import '../../variables';

@media screen and (max-width: 1120px) {
    .single-blog {
        .row {
            .sidebar {
                display: none;
            }

            .fixed-sidebar {
                background-color: $color-black;
                color: $color-white;
                position: fixed;
                width: 100%;
                left: 0;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: $default-gap;
                padding: 20px 24px;

                .side-bar-content {
                    width: 95%;

                    .sidebar-profile {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .close-sidebar {
                            display: block;
                        }

                        .profile-header {
                            display: flex;
                            align-items: center;
                            gap: $default-gap;
                            padding-top: 20px;

                            img {
                                width: 40px;
                                height: 40px;
                                border-radius: 50%;
                            }

                            h2 {
                                font-size: 16px;
                            }

                            a {
                                font-size: 12px;
                                color: $color-white;
                            }
                        }
                    }
                }

                .close-sidebar {
                    display: block;
                    text-align: left;
                }

                h3 {
                    font-size: 20px;
                }

                .col-1 {
                    display: flex;
                    flex-direction: column;
                    gap: $default-gap;
                    border-radius: $border-radius;
                    background-color: $color-light-black;
                    padding: 20px;

                    p {
                        font-size: 16px;
                    }
                }

                .profile {
                    display: flex;
                    align-items: center;
                    gap: $default-gap;
                    padding-top: 20px;

                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }

                    h2 {
                        font-size: 16px;
                    }

                    a {
                        font-size: 12px;
                        color: $color-white;
                    }
                }
            }

            .contents {
                width: 100%;

                display: flex;
                align-items: center;
                justify-content: space-between;

                .nav-btn {
                    display: block;
                }


                .row {
                    .col {
                        padding: 0 20px;
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 768px) {
    .single-blog {
        .row {
            .contents {
                nav {
                    .links {
                        display: none;
                    }
                }

                .row {
                    .blog {
                        width: 100%;

                        .blog-header {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;

                            button {
                                background-color: $color-blue;
                                padding: 12px;
                                font-size: 16px;
                            }

                            .minus-btn {
                                display: none;
                            }
                        }

                        .blog-content {
                            img {
                                width: 100%;
                            }
                        }
                    }

                    .blog-col {
                        display: none;
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 540px) {
    .single-blog {
        .row {
            .contents {
                .row {
                    .blog-col {
                        display: none;
                        width: 100%;
                        top: 50px;
                        bottom: 0;
                        background-color: $color-gray-secondary;
                    }
                }
            }
        }
    }
}