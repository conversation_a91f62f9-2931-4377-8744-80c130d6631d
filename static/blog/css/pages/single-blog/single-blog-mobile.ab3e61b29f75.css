@media screen and (max-width: 1120px) {
  .single-blog .row .sidebar {
    display: none;
  }
  .single-blog .row .fixed-sidebar {
    background-color: #000;
    color: #FFFFFF;
    position: fixed;
    width: 100%;
    left: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 24px;
  }
  .single-blog .row .fixed-sidebar .side-bar-content {
    width: 95%;
  }
  .single-blog .row .fixed-sidebar .side-bar-content .sidebar-profile {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .single-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .close-sidebar {
    display: block;
  }
  .single-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .single-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .single-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header h2 {
    font-size: 16px;
  }
  .single-blog .row .fixed-sidebar .side-bar-content .sidebar-profile .profile-header a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .single-blog .row .fixed-sidebar .close-sidebar {
    display: block;
    text-align: left;
  }
  .single-blog .row .fixed-sidebar h3 {
    font-size: 20px;
  }
  .single-blog .row .fixed-sidebar .col-1 {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 12px;
    background-color: #3F3F3F;
    padding: 20px;
  }
  .single-blog .row .fixed-sidebar .col-1 p {
    font-size: 16px;
  }
  .single-blog .row .fixed-sidebar .profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;
  }
  .single-blog .row .fixed-sidebar .profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .single-blog .row .fixed-sidebar .profile h2 {
    font-size: 16px;
  }
  .single-blog .row .fixed-sidebar .profile a {
    font-size: 12px;
    color: #FFFFFF;
  }
  .single-blog .row .contents {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .single-blog .row .contents .nav-btn {
    display: block;
  }
  .single-blog .row .contents .row .col {
    padding: 0 20px;
  }
}
@media screen and (max-width: 768px) {
  .single-blog .row .contents nav .links {
    display: none;
  }
  .single-blog .row .contents .row .blog {
    width: 100%;
  }
  .single-blog .row .contents .row .blog .blog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .single-blog .row .contents .row .blog .blog-header button {
    background-color: #1890FF;
    padding: 12px;
    font-size: 16px;
  }
  .single-blog .row .contents .row .blog .blog-header .minus-btn {
    display: none;
  }
  .single-blog .row .contents .row .blog .blog-content img {
    width: 100%;
  }
  .single-blog .row .contents .row .blog-col {
    display: none;
  }
}
@media screen and (max-width: 540px) {
  .single-blog .row .contents .row .blog-col {
    display: none;
    width: 100%;
    top: 50px;
    bottom: 0;
    background-color: #EEEEEE;
  }
}/*# sourceMappingURL=single-blog-mobile.ab3e61b29f75.css.map */