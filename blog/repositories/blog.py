from blog.models import *


class CategoryRepository:
    @staticmethod
    def get_or_create_category(name):
        category_obj, created = Category.objects.get_or_create(name=name)
        return category_obj


class AuthorRepository:
    @staticmethod
    def get_or_create_author(first_name, last_name=None, email=None):
        author, created = Author.objects.get_or_create(
            email=email,
            defaults={
                "first_name": first_name,
                "last_name": last_name,
            },
        )
        return author


class TagRepository:
    @staticmethod
    def get_or_create_tag(name):
        tag_obj, created = Tag.objects.get_or_create(name=name)
        return tag_obj


class BlogRepository:
    @staticmethod
    def create_blog(
        title,
        content,
        short_description=None,
        featured_image=None,
        authors=None,
        categories=None,
        tags=None,
        departments=None,
    ):
        blog_obj, created = Blog.objects.get_or_create(
            title=title,
            content=content,
            short_description=short_description,
            featured_image=featured_image,
        )
        if created:
            if authors:
                blog_obj.authors.add(*authors)
            if categories:
                blog_obj.categories.add(*categories)
            if tags:
                blog_obj.tags.add(*tags)
            if departments:
                blog_obj.department.add(*departments)
        return blog_obj if created else None


class DepartmentRepository:
    @staticmethod
    def get_or_create_department(name):
        department_obj, created = Department.objects.get_or_create(name=name)
        return department_obj
