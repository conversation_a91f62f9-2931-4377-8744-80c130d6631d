from dataclasses import dataclass
from typing import Optional, Union, Dict, List
from django.db import models
from django.db.models import Q
from blog.models import Comment
from core.services.logging import LoggingService


@dataclass
class RepositoryResponse:
    success: bool
    message: str
    data: Optional[Union[Dict, List, models.Model]]


class CommentRepository:
    @staticmethod
    def get_all_comments() -> RepositoryResponse:
        try:
            comments = Comment.objects.all()
            return RepositoryResponse(
                success=True, message="Comments retrieved successfully", data=comments
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving all comments", data=None
            )

    @staticmethod
    def get_comment_by_id(comment_id: int) -> RepositoryResponse:
        try:
            comment = Comment.objects.get(id=comment_id)
            return RepositoryResponse(
                success=True, message="Comment retrieved successfully", data=comment
            )
        except Comment.DoesNotExist:
            return RepositoryResponse(
                success=False, message="Comment not found", data=None
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving comment", data=None
            )

    @staticmethod
    def get_comments_by_blog_id(blog_id: int) -> RepositoryResponse:
        try:
            comments = Comment.objects.filter(blog_id=blog_id)
            return RepositoryResponse(
                success=True, message="Comments retrieved successfully", data=comments
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving comments by blog ID", data=None
            )

    @staticmethod
    def get_comments_by_status(is_approved: bool) -> RepositoryResponse:
        try:
            comments = Comment.objects.filter(is_approved=is_approved)
            return RepositoryResponse(
                success=True, message="Comments retrieved successfully", data=comments
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving comments by status", data=None
            )

    @staticmethod
    def get_comments_by_user_id(user_id: str) -> RepositoryResponse:
        try:
            comments = Comment.objects.filter(user_id=user_id)
            return RepositoryResponse(
                success=True, message="Comments retrieved successfully", data=comments
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving comments by user ID", data=None
            )

    @staticmethod
    def get_replies_for_comment(comment_id: int) -> RepositoryResponse:
        try:
            replies = Comment.objects.filter(parent_id=comment_id)
            return RepositoryResponse(
                success=True, message="Replies retrieved successfully", data=replies
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving replies for comment", data=None
            )

    @staticmethod
    def get_approved_comments_for_blog(blog_id: int) -> RepositoryResponse:
        try:
            comments = Comment.objects.filter(blog_id=blog_id, is_approved=True)
            return RepositoryResponse(
                success=True,
                message="Approved comments retrieved successfully",
                data=comments,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error retrieving approved comments for blog",
                data=None,
            )

    @staticmethod
    def get_unapproved_comments_for_blog(blog_id: int) -> RepositoryResponse:
        try:
            comments = Comment.objects.filter(blog_id=blog_id, is_approved=False)
            return RepositoryResponse(
                success=True,
                message="Unapproved comments retrieved successfully",
                data=comments,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error retrieving unapproved comments for blog",
                data=None,
            )

    @staticmethod
    def create_comment(data: Dict) -> RepositoryResponse:
        try:
            comment = Comment.objects.create(
                blog_id=data.get("blog_id"),
                parent_id=data.get("parent_id"),
                email=data.get("email"),
                first_name=data.get("first_name"),
                last_name=data.get("last_name"),
                text=data.get("text"),
                is_approved=False,  # Default to unapproved
            )
            return RepositoryResponse(
                success=True, message="Comment created successfully", data=comment
            )
        except Exception as e:
            LoggingService().log_error(e)
            return RepositoryResponse(
                success=False, message=f"Error creating comment: {str(e)}", data=None
            )
