from django.shortcuts import render, redirect
import os
from django.http import HttpResponse
from django.template import loader
from django.http import HttpResponse
from .models import *
from django.conf import settings

from django.core.files.storage import FileSystemStorage
from django.utils.text import slugify
# Create your views here.


class BlogImagesStorage(FileSystemStorage):
    def get_valid_name(self, name):
        pos_title = self.kwargs[pos_title]
        slug = slugify(pos_title)

        path = super().get_valid_name(name)
        path = f"blogs/images/{slug}/{path}"

        return path


def blog(request, slug):
    blog = Blog.objects.get(slug=slug)
    page_title = blog.title
    page_image = blog.featured_image.url
    print(page_image)
    context = {'blog': blog, 'page_title': page_title, "page_image":page_image}

    return render(request, 'blog/user-single-blog.html', context)


# blog
def blogs(request):
    page_title = 'Blog posts'
    blogs = Blog.objects.all().order_by('-date_created')
    page_image = blogs[0].featured_image.url
    context = {'blogs': blogs, 'page_title': page_title,"page_image":page_image}

    return render(request, 'blog/blog.html', context)


def blogCategories(request):
    categories = Category.objects.all()

    return render(request, 'adminDashboard.html')


def blogCategory(request):
    return render(request, 'adminDashboard.html')
