from rest_framework import serializers

from blog.models import Comment


class BlogSerializer:
    @staticmethod
    def serialize_blog(blog_obj):
        return {
            "id": blog_obj.id,
            "title": blog_obj.title,
            "slug": blog_obj.slug,
            "featured_image": blog_obj.featured_image,
            "categories": [
                {
                    "name": category.name,
                    "slug": category.slug,
                }
                for category in blog_obj.categories.all()
            ],
            "tags": [
                {
                    "name": tag.name,
                    "slug": tag.slug,
                }
                for tag in blog_obj.tags.all()
            ],
            "authors": [
                {
                    "id": author.id,
                    "first_name": author.first_name,
                    "last_name": author.last_name,
                    "email": author.email,
                }
                for author in blog_obj.authors.all()
            ],
            "departments": [
                {
                    "id": department.id,
                    "name": department.name,
                }
                for department in blog_obj.department.all()
            ],
            "short_description": blog_obj.short_description,
            "content": blog_obj.content,
            "date_created": blog_obj.date_created,
            "last_updated": blog_obj.last_updated,
        }


# serializers.py


class CommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Comment
        fields = [
            "id",
            "blog",
            "user",
            "email",
            "first_name",
            "last_name",
            "text",
            "parent",
            "is_approved",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "user", "is_approved", "created_at", "updated_at"]

    def validate(self, data):
        if (
            not data.get("email")
            or not data.get("first_name")
            or not data.get("last_name")
        ):
            raise serializers.ValidationError(
                "Email, first name, and last name are required."
            )
        return data
