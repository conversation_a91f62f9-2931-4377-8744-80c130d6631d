# Generated by Django 5.1 on 2024-08-31 10:43

import ckeditor_uploader.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0007_rename_category_category_name_rename_tab_tag_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='Author',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=244)),
                ('last_name', models.Char<PERSON>ield(max_length=244)),
                ('email', models.EmailField(max_length=254, unique=True)),
            ],
        ),
        migrations.Alter<PERSON>ield(
            model_name='blog',
            name='content',
            field=ckeditor_uploader.fields.RichTextUploadingField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='category',
            name='slug',
            field=models.SlugField(max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='tag',
            name='slug',
            field=models.SlugField(max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='blog',
            name='authors',
            field=models.ManyToManyField(blank=True, to='blog.author'),
        ),
    ]
