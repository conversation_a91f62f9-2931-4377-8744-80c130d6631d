# Generated by Django 5.1 on 2024-08-31 08:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0004_alter_blog_blog_content'),
    ]

    operations = [
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tab', models.CharField(max_length=255)),
                ('slug', models.SlugField(max_length=255, null=None, unique=True)),
            ],
        ),
        migrations.RenameField(
            model_name='blog',
            old_name='blog_summary',
            new_name='short_description',
        ),
        migrations.RemoveField(
            model_name='blog',
            name='blog_content',
        ),
        migrations.AddField(
            model_name='blog',
            name='content',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='blog',
            name='last_updated',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='blog',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blog.author'),
        ),
        migrations.RemoveField(
            model_name='blog',
            name='category',
        ),
        migrations.AlterField(
            model_name='blog',
            name='featured_image',
            field=models.URLField(blank=True, max_length=1500, null=True),
        ),
        migrations.AlterField(
            model_name='blog',
            name='slug',
            field=models.SlugField(max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='blog',
            name='category',
            field=models.ManyToManyField(blank=True, to='blog.category'),
        ),
    ]
