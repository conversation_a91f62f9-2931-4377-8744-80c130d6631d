# Generated by Django 5.1 on 2024-08-31 09:40

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0005_tag_rename_blog_summary_blog_short_description_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='blog',
            name='author',
        ),
        migrations.RenameField(
            model_name='blog',
            old_name='category',
            new_name='categories',
        ),
        migrations.AddField(
            model_name='blog',
            name='authors',
            field=models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='Author',
        ),
    ]
