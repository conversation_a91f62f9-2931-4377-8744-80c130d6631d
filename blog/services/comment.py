from dataclasses import dataclass
from typing import Dict, List, Optional, Union, TypeVar
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models.query import QuerySet

from blog.models import Comment
from blog.repositories.comment import CommentRepository
from core.services.logging import LoggingService

T = TypeVar("T")


@dataclass
class ServiceResponse:
    success: bool
    message: str
    data: Optional[Union[Dict, List, T]] = None
    status_code: int = 200

    @classmethod
    def success_response(
        cls, data: Optional[Union[Dict, List, T]] = None, message: str = "Success"
    ) -> "ServiceResponse":
        return cls(success=True, message=message, data=data, status_code=200)

    @classmethod
    def error_response(cls, message: str, status_code: int = 400) -> "ServiceResponse":
        return cls(success=False, message=message, data=None, status_code=status_code)


class CommentService:
    DEFAULT_PAGE_SIZE = 20

    def __init__(self):
        self.repository = CommentRepository()
        self.logger = LoggingService()

    def _paginate(
        self, queryset: QuerySet, page: int, page_size: int
    ) -> ServiceResponse:
        try:
            paginator = Paginator(queryset, page_size)
            try:
                page_obj = paginator.page(page)
            except PageNotAnInteger:
                page_obj = paginator.page(1)
            except EmptyPage:
                page_obj = paginator.page(paginator.num_pages)

            data = {
                "items": list(page_obj),
                "total": paginator.count,
                "pages": paginator.num_pages,
                "current": page_obj.number,
                "has_next": page_obj.has_next(),
                "has_prev": page_obj.has_previous(),
            }
            return ServiceResponse.success_response(data)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(f"Pagination error: {str(e)}")

    def get_comments(
        self, page: int = 1, page_size: int = DEFAULT_PAGE_SIZE
    ) -> ServiceResponse:
        try:
            response = self.repository.get_all_comments()
            if not response.success:
                return ServiceResponse.error_response(response.message)
            return self._paginate(response.data, page, page_size)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(f"Error fetching comments: {str(e)}")

    def get_comment(self, comment_id: int) -> ServiceResponse:
        try:
            response = self.repository.get_comment_by_id(comment_id)
            if not response.success:
                return ServiceResponse.error_response(response.message)
            return ServiceResponse.success_response(response.data)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(f"Error fetching comment: {str(e)}")

    def get_blog_comments(
        self,
        blog_id: int,
        approved_only: bool = False,
        page: int = 1,
        page_size: int = DEFAULT_PAGE_SIZE,
    ) -> ServiceResponse:
        try:
            if approved_only:
                response = self.repository.get_approved_comments_for_blog(blog_id)
            else:
                response = self.repository.get_comments_by_blog_id(blog_id)

            if not response.success:
                return ServiceResponse.error_response(response.message)
            return self._paginate(response.data, page, page_size)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(
                f"Error fetching blog comments: {str(e)}"
            )

    def get_user_comments(
        self, user_id: str, page: int = 1, page_size: int = DEFAULT_PAGE_SIZE
    ) -> ServiceResponse:
        try:
            response = self.repository.get_comments_by_user_id(user_id)
            if not response.success:
                return ServiceResponse.error_response(response.message)
            return self._paginate(response.data, page, page_size)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(
                f"Error fetching user comments: {str(e)}"
            )

    def get_comment_replies(
        self, comment_id: int, page: int = 1, page_size: int = DEFAULT_PAGE_SIZE
    ) -> ServiceResponse:
        try:
            response = self.repository.get_replies_for_comment(comment_id)
            if not response.success:
                return ServiceResponse.error_response(response.message)
            return self._paginate(response.data, page, page_size)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(
                f"Error fetching comment replies: {str(e)}"
            )

    def get_moderation_queue(
        self,
        blog_id: Optional[int] = None,
        page: int = 1,
        page_size: int = DEFAULT_PAGE_SIZE,
    ) -> ServiceResponse:
        try:
            if blog_id:
                response = self.repository.get_unapproved_comments_for_blog(blog_id)
            else:
                response = self.repository.get_comments_by_status(is_approved=False)

            if not response.success:
                return ServiceResponse.error_response(response.message)
            return self._paginate(response.data, page, page_size)
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(
                f"Error fetching moderation queue: {str(e)}"
            )

    def approve_comment(self, comment_id: int) -> ServiceResponse:
        try:
            comment_response = self.get_comment(comment_id)
            if not comment_response.success:
                return comment_response

            comment = comment_response.data
            comment.is_approved = True
            comment.save()
            return ServiceResponse.success_response(
                comment, "Comment approved successfully"
            )
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(f"Error approving comment: {str(e)}")

    def create_comment(self, data: Dict) -> ServiceResponse:
        try:
            response = self.repository.create_comment(data)
            if not response.success:
                return ServiceResponse.error_response(response.message)
            return ServiceResponse.success_response(
                response.data, "Comment created successfully"
            )
        except Exception as e:
            self.logger.log_error(e)
            return ServiceResponse.error_response(f"Error creating comment: {str(e)}")
