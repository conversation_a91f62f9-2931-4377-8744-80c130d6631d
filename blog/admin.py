from django.contrib import admin
from .models import *

# Register your models here.


@admin.register(Blog)
class blogAdmin(admin.ModelAdmin):
    # , 'author', 'date_created', 'status')
    list_display = ("title", "status", "date_created")
    # prepopulated_fields = {"slug": ("title",)}


@admin.register(Category)
class categoryAdmin(admin.ModelAdmin):
    list_display = ("name", "slug")
    prepopulated_fields = {"slug": ("name",)}


@admin.register(Tag)
class categoryAdmin(admin.ModelAdmin):
    list_display = ("name", "slug")
    prepopulated_fields = {"slug": ("name",)}


@admin.register(Department)
class departmentAdmin(admin.ModelAdmin):
    list_display = ("name",)


@admin.register(Author)
class authorAdmin(admin.ModelAdmin):
    list_display = ("first_name", "last_name", "email")


@admin.register(APIKey)
class APIKeysAdmin(admin.ModelAdmin):
    list_display = ("user", "key")
