# tests/test_repositories.py
from django.test import TestCase

from blog.factory import *
from blog.repositories.blog import *


class BlogRepositoryTest(TestCase):
    def test_create_blog(self):
        # Create related objects
        category = CategoryFactory()
        author = AuthorFactory()
        tag = TagFactory()
        department = DepartmentRepository()

        # Create a blog
        blog = BlogRepository.create_blog(
            title="Test Blog",
            content="Test content",
            short_description="Test description",
            featured_image="http://example.com/image.jpg",
            authors=[author],
            categories=[category],
            tags=[tag],
            departments=[department],
        )

        # Assertions
        self.assertIsNotNone(blog)
        self.assertEqual(blog.title, "Test Blog")
        self.assertEqual(blog.authors.count(), 1)
        self.assertEqual(blog.categories.count(), 1)
        self.assertEqual(blog.tags.count(), 1)
        self.assertEqual(blog.department.count(), 1)
