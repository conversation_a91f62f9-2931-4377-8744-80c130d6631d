# tests/test_services.py
from django.test import TestCase

from blog.services.blog import BlogService


class BlogServiceTest(TestCase):
    def test_create_blog_success(self):
        blog_data = {
            "title": "Test Blog",
            "content": "Test content",
            "categories": [{"name": "Category 1"}],
            "authors": [
                {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>"}
            ],
            "tags": [{"name": "Tag 1"}],
            "departments": [{"name": "Department 1"}],
        }

        result = BlogService.create_blog(blog_data)

        self.assertIn("message", result)
        self.assertEqual(result["message"], "Blog created successfully")
        self.assertIn("blog", result)

    def test_create_blog_missing_required_field(self):
        blog_data = {
            "content": "Test content",
            "categories": [{"name": "Category 1"}],
        }

        result = BlogService.create_blog(blog_data)

        self.assertIn("error", result)
        self.assertEqual(result["error"], "Title is required")

    def test_create_blog_with_duplicate_title(self):
        blog_data = {
            "title": "Duplicate Blog",
            "content": "Test content",
            "categories": [{"name": "Category 1"}],
        }
        BlogService.create_blog(blog_data)

        result = BlogService.create_blog(blog_data)

        # Assertions
        self.assertIn("error", result)
        self.assertEqual(result["error"], "A blog with this title already exists")
