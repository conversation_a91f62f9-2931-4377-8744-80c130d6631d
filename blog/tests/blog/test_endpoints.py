# tests/test_views.py
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from core.services.tests.base_setup import BaseSetup


class BlogApiViewTest(BaseSetup):
    def test_create_blog_success(self):
        url = reverse("new_blog_api")
        data = {
            "title": "Test Blog",
            "content": "Test content",
            "categories": [{"name": "Category 1"}],
            "authors": [
                {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>"}
            ],
            "tags": [{"name": "Tag 1"}],
            "departments": [{"name": "Department 1"}],
        }

        self._authenticate_user(self.user)
        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("message", response.data)
        self.assertEqual(response.data["message"], "Blog created successfully")

    def test_create_blog_missing_required_field(self):
        url = reverse("new_blog_api")
        data = {
            "content": "Test content",
            "categories": [{"name": "Category 1"}],
        }

        self._authenticate_user(self.user)
        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertEqual(response.data["error"], "Title is required")
