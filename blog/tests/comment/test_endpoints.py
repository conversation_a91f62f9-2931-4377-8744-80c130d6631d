from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from blog.factory import BlogFactory, CommentFactory
from blog.models import Comment
from core.services.tests.base_setup import BaseSetup


class BlogCommentApiTest(BaseSetup):
    def setUp(self):
        super().setUp()
        self.blog = BlogFactory()
        self.comment = CommentFactory(
            blog=self.blog,
            is_approved=True,
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            text="Test comment",
        )

    def test_get_blog_comments_success(self):
        print("Comments in the database: ", Comment.objects.all().count())
        url = reverse("get_blog_comments", kwargs={"blog_id": self.blog.id})
        response = self.client.get(url)
        print("response: ", response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("data", response.data)
        self.assertIn("meta", response.data)
        self.assertEqual(len(response.data["data"]), 1)
        self.assertEqual(response.data["data"][0]["blog"], self.blog.id)

    def test_get_blog_comments_nonexistent_blog(self):
        url = reverse("get_blog_comments", kwargs={"blog_id": 99999})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["data"]), 0)

    def test_get_blog_comments_pagination(self):
        [CommentFactory(blog=self.blog, is_approved=True) for _ in range(3)]
        url = reverse("get_blog_comments", kwargs={"blog_id": self.blog.id})
        response = self.client.get(f"{url}?page=1&page_size=2")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["data"]), 2)
        self.assertTrue(response.data["meta"]["has_next"])
        # self.assertEqual(response.data["meta"]["total"], 4)  # 3 new + 1 from setUp

    def test_get_blog_comments_filter_approved(self):
        approved_comment = CommentFactory(blog=self.blog, is_approved=True)
        url = reverse("get_blog_comments", kwargs={"blog_id": self.blog.id})
        response = self.client.get(f"{url}?approved_only=true")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["data"]), 2)
        self.assertTrue(response.data["data"][0]["is_approved"])

    def test_approve_comment_success(self):
        url = reverse(
            "approve_comment",
            kwargs={"blog_id": self.blog.id, "comment_id": self.comment.id},
        )
        self._authenticate_user(self.user)
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("message", response.data)
        self.assertEqual(response.data["message"], "Comment approved successfully")

        self.comment.refresh_from_db()
        self.assertTrue(self.comment.is_approved)

    def test_approve_comment_unauthorized(self):
        url = reverse(
            "approve_comment",
            kwargs={"blog_id": self.blog.id, "comment_id": self.comment.id},
        )
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_approve_comment_wrong_blog(self):
        other_blog = BlogFactory()
        url = reverse(
            "approve_comment",
            kwargs={"blog_id": other_blog.id, "comment_id": self.comment.id},
        )
        self._authenticate_user(self.user)
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertEqual(response.data["error"], "Comment does not belong to this blog")

    def test_create_comment_success(self):
        url = reverse("create_comment", kwargs={"blog_id": self.blog.id})
        data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "text": "New comment",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("message", response.data)
        self.assertIn("data", response.data)
        self.assertEqual(response.data["data"]["blog"], self.blog.id)
        self.assertEqual(response.data["data"]["email"], "<EMAIL>")
        self.assertFalse(response.data["data"]["is_approved"])

    def test_create_comment_missing_required_fields(self):
        url = reverse("create_comment", kwargs={"blog_id": self.blog.id})
        data = {"first_name": "Jane", "last_name": "Doe"}
        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    def test_create_reply_to_comment(self):
        url = reverse("create_comment", kwargs={"blog_id": self.blog.id})
        data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "text": "Reply comment",
            "parent": self.comment.id,
        }
        response = self.client.post(url, data, format="json")
        print("Response data:", response.data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_reply_to_nonexistent_comment(self):
        url = reverse("create_comment", kwargs={"blog_id": self.blog.id})
        data = {
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "text": "Reply comment",
            "parent": 99999,
        }
        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
