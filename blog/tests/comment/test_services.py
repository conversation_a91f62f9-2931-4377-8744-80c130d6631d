from django.test import TestCase

from blog.factory import BlogFactory, CommentFactory
from blog.services.comment import CommentService


class CommentServiceTest(TestCase):
    def setUp(self):
        self.service = CommentService()
        self.blog = BlogFactory()
        self.comment = CommentFactory(
            blog=self.blog,
            is_approved=False,
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            text="Test comment",
        )

    def test_get_comments_pagination(self):
        [CommentFactory(blog=self.blog) for _ in range(5)]
        response = self.service.get_comments(page=1, page_size=2)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 2)
        self.assertEqual(response.data["total"], 6)  # 5 new + 1 from setUp
        self.assertEqual(response.data["pages"], 3)
        self.assertTrue(response.data["has_next"])
        self.assertFalse(response.data["has_prev"])

    def test_get_comments_last_page(self):
        [CommentFactory(blog=self.blog) for _ in range(3)]
        response = self.service.get_comments(page=2, page_size=2)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 2)
        self.assertTrue(response.data["has_prev"])
        self.assertFalse(response.data["has_next"])

    def test_get_comments_invalid_page(self):
        response = self.service.get_comments(page=999, page_size=2)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 1)  # Only one comment from setUp
        self.assertEqual(response.data["current"], 1)
        self.assertFalse(response.data["has_next"])

    def test_get_blog_comments_success(self):
        response = self.service.get_blog_comments(self.blog.id)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 1)
        self.assertEqual(response.data["items"][0].blog_id, self.blog.id)

    def test_get_blog_comments_approved_only(self):
        approved_comment = CommentFactory(blog=self.blog, is_approved=True)
        response = self.service.get_blog_comments(self.blog.id, approved_only=True)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 1)
        self.assertTrue(all(comment.is_approved for comment in response.data["items"]))

    def test_get_comment_replies_success(self):
        reply = CommentFactory(blog=self.blog, parent=self.comment)
        response = self.service.get_comment_replies(self.comment.id)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 1)
        self.assertEqual(response.data["items"][0].parent_id, self.comment.id)

    def test_get_moderation_queue_success(self):
        response = self.service.get_moderation_queue()

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 1)
        self.assertTrue(
            all(not comment.is_approved for comment in response.data["items"])
        )

    def test_get_moderation_queue_with_blog(self):
        other_blog = BlogFactory()
        unapproved_other = CommentFactory(blog=other_blog, is_approved=False)

        response = self.service.get_moderation_queue(blog_id=self.blog.id)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 1)
        self.assertEqual(response.data["items"][0].blog_id, self.blog.id)

    def test_approve_comment_success(self):
        response = self.service.approve_comment(self.comment.id)

        self.assertTrue(response.success)
        self.comment.refresh_from_db()
        self.assertTrue(self.comment.is_approved)

    def test_approve_nonexistent_comment(self):
        response = self.service.approve_comment(999)

        self.assertFalse(response.success)

    def test_create_comment_success(self):
        data = {
            "blog_id": self.blog.id,
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "text": "New comment",
        }
        response = self.service.create_comment(data)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Comment created successfully")
        self.assertEqual(response.data.email, "<EMAIL>")
        self.assertFalse(response.data.is_approved)

    def test_create_comment_missing_field(self):
        data = {"blog_id": self.blog.id, "first_name": "Jane"}
        response = self.service.create_comment(data)

        self.assertFalse(response.success)
        self.assertIn("Error", response.message)
        self.assertIsNone(response.data)

    def test_pagination_empty_results(self):
        response = self.service.get_blog_comments(99999)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["items"]), 0)
        self.assertEqual(response.data["total"], 0)
        self.assertEqual(response.data["pages"], 1)
        self.assertFalse(response.data["has_next"])
        self.assertFalse(response.data["has_prev"])

    def test_get_comment_success(self):
        response = self.service.get_comment(self.comment.id)

        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.comment.id)
        self.assertEqual(response.status_code, 200)

    def test_get_comment_not_found(self):
        response = self.service.get_comment(99999)

        self.assertFalse(response.success)
        self.assertEqual(response.status_code, 400)
