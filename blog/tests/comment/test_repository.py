from django.test import TestCase
from django.db.utils import IntegrityError

from blog.factory import BlogFactory, CommentFactory
from blog.repositories.comment import CommentRepository


class CommentRepositoryTest(TestCase):
    def setUp(self):
        self.repository = CommentRepository()
        self.blog = BlogFactory()
        self.comment = CommentFactory(
            blog=self.blog,
            is_approved=False,
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            text="Test comment",
        )

    def test_get_all_comments_success(self):
        response = self.repository.get_all_comments()

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Comments retrieved successfully")
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].id, self.comment.id)

    def test_get_comment_by_id_success(self):
        response = self.repository.get_comment_by_id(self.comment.id)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Comment retrieved successfully")
        self.assertEqual(response.data.id, self.comment.id)

    def test_get_comment_by_id_not_found(self):
        response = self.repository.get_comment_by_id(999)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Comment not found")
        self.assertIsNone(response.data)

    def test_get_comments_by_blog_id_success(self):
        response = self.repository.get_comments_by_blog_id(self.blog.id)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Comments retrieved successfully")
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].blog_id, self.blog.id)

    def test_get_comments_by_status_success(self):
        response = self.repository.get_comments_by_status(is_approved=False)

        self.assertTrue(response.success)
        self.assertTrue(all(not comment.is_approved for comment in response.data))

    def test_get_replies_for_comment_success(self):
        reply = CommentFactory(blog=self.blog, parent=self.comment)
        response = self.repository.get_replies_for_comment(self.comment.id)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].id, reply.id)

    def test_get_approved_comments_for_blog_success(self):
        approved_comment = CommentFactory(blog=self.blog, is_approved=True)
        response = self.repository.get_approved_comments_for_blog(self.blog.id)

        self.assertTrue(response.success)
        self.assertTrue(all(comment.is_approved for comment in response.data))

    def test_get_unapproved_comments_for_blog_success(self):
        response = self.repository.get_unapproved_comments_for_blog(self.blog.id)

        self.assertTrue(response.success)
        self.assertTrue(all(not comment.is_approved for comment in response.data))

    def test_create_comment_success(self):
        data = {
            "blog_id": self.blog.id,
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Doe",
            "text": "New comment",
        }
        response = self.repository.create_comment(data)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Comment created successfully")
        self.assertEqual(response.data.email, "<EMAIL>")
        self.assertFalse(response.data.is_approved)

    def test_create_comment_missing_required_field(self):
        data = {"blog_id": self.blog.id, "first_name": "Jane"}
        response = self.repository.create_comment(data)

        self.assertFalse(response.success)
        self.assertIn("Error creating comment", response.message)
        self.assertIsNone(response.data)
